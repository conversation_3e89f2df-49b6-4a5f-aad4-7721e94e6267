import 'package:flutter/material.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/theme/app_colors.dart';

class WebLeftPanelSolution extends StatefulWidget {
  const WebLeftPanelSolution({super.key});

  @override
  State<WebLeftPanelSolution> createState() => _WebLeftPanelSolutionState();
}

class _WebLeftPanelSolutionState extends State<WebLeftPanelSolution>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  // Static data for objects
  final List<Map<String, dynamic>> _objectsData = [
    {
      'name': 'Customer',
      'attributes': [
        'Attribute',
        'Attribute',
        'Attribute',
        'Attribute',
        'Attribute',
        'Attribute',
        'Attribute',
      ],
      'isExpanded': false,
    },
    {
      'name': 'Customer 2',
      'attributes': ['Attribute', 'Attribute', 'Attribute'],
      'isExpanded': false,
    },
    {
      'name': 'Customer 3',
      'attributes': ['Attribute', 'Attribute'],
      'isExpanded': false,
    },
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this, initialIndex: 1); // Start with Objects tab selected
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 300,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300, width: 1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: [
          _buildTabBar(),
          _buildSearchField(),
          Expanded(
            child: _buildTabContent(),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: TabBar(
        controller: _tabController,
        labelColor: Colors.black,
        unselectedLabelColor: Colors.grey.shade600,
        indicatorColor: Colors.black,
        indicatorWeight: 2,
        labelStyle: FontManager.getCustomStyle(
          fontSize: FontManager.s14,
          fontWeight: FontManager.medium,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
        unselectedLabelStyle: FontManager.getCustomStyle(
          fontSize: FontManager.s14,
          fontWeight: FontManager.regular,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.grey.shade600,
        ),
        tabs: const [
          Tab(text: 'Roles'),
          Tab(text: 'Objects'),
          Tab(text: 'GO'),
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return Container(
      padding: const EdgeInsets.all(AppSpacing.md),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.grey.shade300, width: 1),
        ),
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Search',
          hintStyle: FontManager.getCustomStyle(
            fontSize: FontManager.s14,
            fontWeight: FontManager.regular,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.grey.shade500,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: BorderSide(color: Colors.grey.shade300),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(4),
            borderSide: const BorderSide(color: AppColors.primaryBlue),
          ),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: AppSpacing.sm,
            vertical: AppSpacing.sm,
          ),
          isDense: true,
        ),
        style: FontManager.getCustomStyle(
          fontSize: FontManager.s14,
          fontWeight: FontManager.regular,
          fontFamily: FontManager.fontFamilyTiemposText,
          color: Colors.black,
        ),
      ),
    );
  }

  Widget _buildTabContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildRolesTab(),
        _buildObjectsTab(),
        _buildGoTab(),
      ],
    );
  }

  Widget _buildRolesTab() {
    return const Center(
      child: Text(
        'Roles content',
        style: TextStyle(
          fontSize: 16,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildObjectsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(AppSpacing.sm),
      itemCount: _objectsData.length,
      itemBuilder: (context, index) {
        final object = _objectsData[index];
        return _buildObjectExpansionTile(object, index);
      },
    );
  }

  Widget _buildGoTab() {
    return const Center(
      child: Text(
        'GO content',
        style: TextStyle(
          fontSize: 16,
          color: Colors.grey,
        ),
      ),
    );
  }

  Widget _buildObjectExpansionTile(Map<String, dynamic> object, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppSpacing.sm),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300, width: 1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: ExpansionTile(
        title: Row(
          children: [
            Text(
              'Object: ${object['name']}',
              style: FontManager.getCustomStyle(
                fontSize: FontManager.s14,
                fontWeight: FontManager.medium,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 20,
              height: 20,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade400, width: 1),
                borderRadius: BorderRadius.circular(2),
              ),
              child: const Icon(
                Icons.add,
                size: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(width: AppSpacing.sm),
            Icon(
              object['isExpanded'] ? Icons.expand_less : Icons.expand_more,
              color: Colors.grey.shade600,
            ),
          ],
        ),
        initiallyExpanded: object['isExpanded'],
        onExpansionChanged: (expanded) {
          setState(() {
            _objectsData[index]['isExpanded'] = expanded;
          });
        },
        tilePadding: const EdgeInsets.symmetric(
          horizontal: AppSpacing.md,
          vertical: AppSpacing.xs,
        ),
        childrenPadding: const EdgeInsets.only(
          left: AppSpacing.md,
          right: AppSpacing.md,
          bottom: AppSpacing.sm,
        ),
        children: [
          ...object['attributes'].map<Widget>((attribute) {
            return Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppSpacing.sm,
                vertical: AppSpacing.xs,
              ),
              margin: const EdgeInsets.only(bottom: AppSpacing.xs),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(2),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      attribute,
                      style: FontManager.getCustomStyle(
                        fontSize: FontManager.s13,
                        fontWeight: FontManager.regular,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:nsl/models/solution_creation_model.dart';
import 'package:nsl/providers/web_home_provider_static.dart';
import 'package:nsl/screens/web/static_flow/customer_onboarding_static.dart';
import 'package:nsl/screens/web/static_flow/extract_details_middle_static.dart';
import 'package:nsl/theme/spacing.dart';
import 'package:nsl/utils/font_manager.dart';
import 'package:nsl/utils/responsive_font_sizes.dart';
import 'package:nsl/services/validation_engine.dart';
import 'package:nsl/widgets/validated_text_field.dart';
import 'package:provider/provider.dart';

class SolutionExpansionHandler {
  /// Build solution expansion panel
  static Widget buildSolutionExpansionPanel(
      BuildContext context, SolutionCreationModel solution) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        final solutionTitle = solution.name;
        final isExpanded = provider.isObjectExpanded(solutionTitle);

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 1),
          decoration: BoxDecoration(
            color: Colors.white,
          ),
          child: Theme(
            data: Theme.of(context).copyWith(
              dividerColor: Colors.transparent,
            ),
            child: ListTileTheme(
              dense: true,
              child: ExpansionTile(
                tilePadding:
                    const EdgeInsets.symmetric(horizontal: 4, vertical: 0),
                childrenPadding: EdgeInsets.zero,
                onExpansionChanged: (expanded) =>
                    provider.setObjectExpansion(solutionTitle, expanded),
                trailing: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: isExpanded
                        ? const Color(0xFF0058FF)
                        : Colors.transparent,
                  ),
                  child: Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up
                        : Icons.keyboard_arrow_down,
                    color: isExpanded ? Colors.white : Colors.grey[600],
                    size: 20,
                  ),
                ),
                title: Row(
                  children: [
                    Expanded(
                      child: Text(
                        solutionTitle,
                        style: FontManager.getCustomStyle(
                          fontSize: _getResponsiveValueFontSize(context),
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: isExpanded ? Colors.black : Color(0xFF242424),
                          fontWeight:
                              isExpanded ? FontWeight.w600 : FontWeight.w400,
                          height: 1.2,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    HoverBellIcon(
                      onTap: () {
                        // Bell icon click action
                      },
                    ),
                  ],
                ),
                children: [
                  buildSolutionDetailsSection(context, solution),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build solution details section with expandable tiles
  static Widget buildSolutionDetailsSection(
      BuildContext context, SolutionCreationModel solution) {
    return Container(
      padding: const EdgeInsets.all(6),
      margin: const EdgeInsets.symmetric(horizontal: 6),
      child: _AccordionSolutionSections(solution: solution),
    );
  }

  /// Build expandable solution section
  static Widget _buildExpandableSolutionSection(
    BuildContext context,
    String title,
    String status,
    String count,
    Widget content,
  ) {
    return Consumer<WebHomeProviderStatic>(
      builder: (context, provider, child) {
        final sectionKey = 'solution_section_$title';
        final isExpanded = provider.isObjectExpanded(sectionKey);

        Color statusColor = Colors.grey;
        Color statusBgColor = Colors.grey[100]!;

        // Set colors based on status
        if (status == 'Partial Completion') {
          statusColor = const Color(0xFF92400E);
          statusBgColor = const Color(0xFFFEF3C7);
        } else if (status == 'Missing') {
          statusColor = const Color(0xFF991B1B);
          statusBgColor = const Color(0xFFFEE2E2);
        }

        return Container(
          margin: const EdgeInsets.symmetric(vertical: 2),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
            borderRadius: BorderRadius.circular(4),
            color: Colors.white,
          ),
          child: Column(
            children: [
              // Header
              InkWell(
                onTap: () =>
                    provider.setObjectExpansion(sectionKey, !isExpanded),
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  child: Row(
                    children: [
                      // Title
                      Expanded(
                        flex: 3,
                        child: Text(
                          title,
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),

                      // Status
                      if (status.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: statusBgColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            status,
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.labelSmall(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: statusColor,
                            ),
                          ),
                        ),

                      const SizedBox(width: 8),

                      // Count
                      Text(
                        count,
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w400,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey[600],
                        ),
                      ),

                      // Arrow icon
                      const SizedBox(width: 8),
                      Icon(
                        isExpanded
                            ? Icons.keyboard_arrow_up
                            : Icons.keyboard_arrow_down,
                        color: Colors.grey[600],
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),

              // Expandable content
              if (isExpanded)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: const BoxDecoration(
                    border: Border(
                      top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                    ),
                  ),
                  child: content,
                ),
            ],
          ),
        );
      },
    );
  }

  /// Get responsive font size (helper method)
  static double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1920) {
      return 18.0;
    } else if (screenWidth > 1440) {
      return 16.0;
    } else if (screenWidth > 1280) {
      return 14.0;
    } else {
      return 12.0;
    }
  }

  /// LO Details content
  static Widget _buildLODetailsContent(
      BuildContext context, SolutionCreationModel solution) {
    return Container(
      decoration: BoxDecoration(
          // border: Border.all(color: const Color(0xFF0058FF), width: 2),
          // borderRadius: BorderRadius.circular(8),
          ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Leave Application Object Configuration',
            style: FontManager.getCustomStyle(
              fontSize: _getResponsiveValueFontSize(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),

          // Form fields
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                _buildLOFormField(
                    context,
                    'Name',
                    solution.loDetails?.leaveApplicationObjectConfiguration
                            ?.name ??
                        'CustomerOnboarding',
                    'Extracted'),
                _buildLOFormField(
                    context,
                    'Display Name',
                    solution.loDetails?.leaveApplicationObjectConfiguration
                            ?.displayName ??
                        'Onboard new customer',
                    'Generated'),
                _buildLOFormField(
                    context,
                    'Workflow Origin',
                    solution.loDetails?.leaveApplicationObjectConfiguration
                            ?.workflowOrigin ??
                        'True',
                    'Inferred'),
                _buildLOFormField(
                    context,
                    'Pathway Type',
                    solution.loDetails?.leaveApplicationObjectConfiguration
                            ?.pathwayType ??
                        'Sequential',
                    'Inferred'),
                _buildLOFormField(
                    context,
                    'Function Type',
                    solution.loDetails?.leaveApplicationObjectConfiguration
                            ?.functionType ??
                        'create_record',
                    'Inferred'),
                _buildLOFormField(
                    context,
                    'Agent Type',
                    solution.loDetails?.leaveApplicationObjectConfiguration
                            ?.agentType ??
                        'HUMAN',
                    'Inferred'),
                _buildLOFormField(
                    context,
                    'UI Type',
                    solution.loDetails?.leaveApplicationObjectConfiguration
                            ?.uiType ??
                        'Form',
                    'Inferred'),

                // Assign Rights for Roles button
                const SizedBox(height: 16),
                Align(
                  alignment: Alignment.centerLeft,
                  child: ElevatedButton(
                    onPressed: () => _showAssignRightsDialog(context),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                    ),
                    child: Text(
                      'Assign Rights for Roles',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),

                // Role Name field
                const SizedBox(height: 16),
                _buildLOFormField(
                    context,
                    'Role Name',
                    solution.loDetails?.leaveApplicationObjectConfiguration
                            ?.roleName ??
                        'Edit Rights, Execution Rights',
                    ''),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build LO form field
  static Widget _buildLOFormField(
      BuildContext context, String label, String value, String status) {
    // Check if this field should have a dropdown
    bool hasDropdown = [
      'Workflow Origin',
      'Pathway Type',
      'Agent Type',
      'UI Type'
    ].contains(label);

    // Get screen width for responsive calculations
    double screenWidth = MediaQuery.of(context).size.width;

    // Calculate responsive widths
    double labelWidth = screenWidth * 0.15; // 15% of screen width
    double valueWidth = screenWidth * 0.45; // 45% of screen width
    double statusWidth = screenWidth * 0.12; // 12% of screen width

    // Set minimum and maximum widths for better control
    labelWidth = labelWidth.clamp(100.0, 150.0);
    valueWidth = valueWidth.clamp(200.0, 400.0);
    statusWidth = statusWidth.clamp(80.0, 120.0);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // Label with fixed responsive width
          SizedBox(
            width: labelWidth,
            child: Text(
              label,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
          ),

          const SizedBox(width: 12), // Fixed spacing between label and value

          // Value field with fixed responsive width
          SizedBox(
            width: valueWidth,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
                borderRadius: BorderRadius.circular(4),
                color: Colors.white,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      value,
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                  ),
                  // Dropdown icon for specific fields
                  if (hasDropdown)
                    Icon(
                      Icons.keyboard_arrow_down,
                      color: Colors.grey[600],
                      size: 16,
                    ),
                ],
              ),
            ),
          ),

          const SizedBox(
              width: AppSpacing.lg), // Fixed spacing between value and status

          // Status with fixed responsive width
          if (status.isNotEmpty)
            SizedBox(
              width: statusWidth,
              child: Text(
                status,
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodySmall(context),
                  fontWeight: FontWeight.w400,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[600],
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Inputs Stack content
  static Widget _buildInputsStackContent(
      BuildContext context, SolutionCreationModel solution) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Using Row to align Object and Attributes labels with their content
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Labels column
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Object:',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Attributes:',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodySmall(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),

              const SizedBox(
                  width: AppSpacing.xl), // Space between labels and content

              // Content column
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'LeaveApplication',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'leaveId*, employeeId*, startDate*, endDate*, numDays*, reason*, leaveTypeName* (Annual Leave, Sick Leave, Personal Leave), leaveSubTypeName* [dependent on leaveTypeName], requiredDocumentation* (true, false), status* (Pending, Approved, Rejected), instructions [information], allowedNumberOfDays [constant], submissionDate*',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w400,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                      softWrap: true,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static Widget _buildOutputStackContent(
      BuildContext context, SolutionCreationModel solution) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with + Add Input button
          Row(
            children: [
              Text(
                'Input Attributes',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyLarge(context),
                  fontWeight: FontWeight.w600,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: () => _showAttributesDialog(context),
                icon: const Icon(Icons.add, size: 16),
                label: Text(
                  'Add Input',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0058FF),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  minimumSize: Size.zero,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                // Table Header
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: const BoxDecoration(
                    color: Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Entity',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Attribute',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Required',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Actions',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Table Row
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: const BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(8),
                      bottomRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Customer',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Customer ID',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          'True',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyMedium(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Row(
                          children: [
                            IconButton(
                              onPressed: () => _showEditAttributeDialog(
                                  context, 'Customer', 'Customer ID', true),
                              icon: const Icon(Icons.edit, size: 16),
                              color: Colors.blue,
                              constraints: const BoxConstraints(),
                              padding: EdgeInsets.zero,
                            ),
                            const SizedBox(width: 8),
                            IconButton(
                              onPressed: () => _showDeleteConfirmation(context),
                              icon: const Icon(Icons.delete_outline, size: 16),
                              color: Colors.red,
                              constraints: const BoxConstraints(),
                              padding: EdgeInsets.zero,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Output Stack content with dynamic data
  static Widget _buildOutputStackContentWithData(
    BuildContext context,
    SolutionCreationModel solution,
    List<Map<String, dynamic>> outputRows, {
    Function(String, String, bool)? onAddRow,
    Function(int, String, String, bool)? onEditRow,
    Function(int)? onDeleteRow,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with + Add Input button
          Row(
            children: [
              Text(
                'Input Attributes',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodySmall(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: () => _showAttributesDialog(
                  context,
                  onApply: onAddRow,
                ),
                icon: const Icon(Icons.add, size: 16),
                label: Text(
                  'Add Input',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0058FF),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                  minimumSize: Size.zero,
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                // Header row
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: const BoxDecoration(
                    color: Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Entity',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Attribute',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Required',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Actions',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Dynamic data rows
                ...outputRows.asMap().entries.map((entry) {
                  final index = entry.key;
                  final row = entry.value;
                  final isLastRow = index == outputRows.length - 1;

                  return Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: isLastRow
                          ? const BorderRadius.only(
                              bottomLeft: Radius.circular(8),
                              bottomRight: Radius.circular(8),
                            )
                          : null,
                      border: index >= 0
                          ? const Border(
                              top: BorderSide(color: Color(0xFFE5E7EB)),
                            )
                          : null,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['entity'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['attribute'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            (row['required'] ?? false).toString(),
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Row(
                            children: [
                              IconButton(
                                onPressed: () => _showAttributesDialog(
                                  context,
                                  entity: row['entity'],
                                  attribute: row['attribute'],
                                  required: row['required'],
                                  onApply: (entity, attribute, required) =>
                                      onEditRow?.call(
                                          index, entity, attribute, required),
                                ),
                                icon: const Icon(Icons.edit, size: 16),
                                color: Colors.blue,
                                constraints: const BoxConstraints(),
                                padding: EdgeInsets.zero,
                              ),
                              const SizedBox(width: 8),
                              IconButton(
                                onPressed: () => onDeleteRow?.call(index),
                                icon:
                                    const Icon(Icons.delete_outline, size: 16),
                                color: Colors.red,
                                constraints: const BoxConstraints(),
                                padding: EdgeInsets.zero,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Validation Stack content with dynamic data
  static Widget _buildValidationStackContentWithData(
    BuildContext context,
    SolutionCreationModel solution,
    List<Map<String, dynamic>> validationRows, {
    Function(String, String, String, String, String, String)? onAddRow,
    Function(int, String, String, String, String, String, String)? onEditRow,
    Function(int)? onDeleteRow,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with + Add Input button
          Row(
            children: [
              Text(
                'Validation Rules',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodySmall(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              const Spacer(),
              Row(
                children: [
                  ElevatedButton.icon(
                    onPressed: () =>
                        SolutionExpansionHandler._showValidationRulesDialog(
                      context,
                      onApply: onAddRow,
                    ),
                    icon: const Icon(Icons.add, size: 16),
                    label: Text(
                      'Add Input',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodySmall(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.white,
                      ),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF0058FF),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 8, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      minimumSize: Size.zero,
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: const Color(0xFFE5E7EB)),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                // Header row
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  decoration: const BoxDecoration(
                    color: Color(0xFFF8F9FA),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Attribute',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Validation Function',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Success Value',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Actions',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Dynamic data rows
                ...validationRows.asMap().entries.map((entry) {
                  final index = entry.key;
                  final row = entry.value;
                  final isLastRow = index == validationRows.length - 1;

                  return Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: isLastRow
                          ? const BorderRadius.only(
                              bottomLeft: Radius.circular(8),
                              bottomRight: Radius.circular(8),
                            )
                          : null,
                      border: index >= 0
                          ? const Border(
                              top: BorderSide(color: Color(0xFFE5E7EB)),
                            )
                          : null,
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['attribute'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['validationFunction'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            row['successCondition'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Row(
                            children: [
                              IconButton(
                                onPressed: () => SolutionExpansionHandler
                                    ._showValidationRulesDialog(
                                  context,
                                  attribute: row['attribute'],
                                  validationFunction: row['validationFunction'],
                                  successCondition: row['successCondition'],
                                  failureCondition: row['failureCondition'],
                                  successMessage: row['successMessage'],
                                  failureMessage: row['failureMessage'],
                                  onApply: (attr, valFunc, succCond, failCond,
                                          succMsg, failMsg) =>
                                      onEditRow?.call(index, attr, valFunc,
                                          succCond, failCond, succMsg, failMsg),
                                ),
                                icon: const Icon(Icons.edit, size: 16),
                                color: Colors.blue,
                                constraints: const BoxConstraints(),
                                padding: EdgeInsets.zero,
                              ),
                              const SizedBox(width: 8),
                              IconButton(
                                onPressed: () => onDeleteRow?.call(index),
                                icon:
                                    const Icon(Icons.delete_outline, size: 16),
                                color: Colors.red,
                                constraints: const BoxConstraints(),
                                padding: EdgeInsets.zero,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// UI Stack content
  static Widget _buildUIStackContent(
      BuildContext context, SolutionCreationModel solution) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'UI Stack Configuration',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyLarge(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Configured: ${solution.uiStack?.configuredCount ?? 2}',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[600],
            ),
          ),
          Text(
            'Status: ${solution.uiStack?.status ?? 'Missing'}',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// UI Stack content with data table and dialog
  static Widget _buildUIStackContentWithData(
    BuildContext context,
    SolutionCreationModel solution,
    List<Map<String, dynamic>> uiStackRows, {
    required Function(
            String,
            String,
            String,
            String,
            String,
            int,
            String,
            String,
            String,
            List<Map<String, dynamic>>,
            List<Map<String, dynamic>>,
            List<Map<String, dynamic>>)
        onAddRow,
    required Function(
            int,
            String,
            String,
            String,
            String,
            String,
            int,
            String,
            String,
            String,
            List<Map<String, dynamic>>,
            List<Map<String, dynamic>>,
            List<Map<String, dynamic>>)
        onEditRow,
    required Function(int) onDeleteRow,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Validation Rules',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodySmall(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.black,
                ),
              ),
              ElevatedButton.icon(
                onPressed: () =>
                    _showUIControlsDialog(context, onAddRow, null, null),
                icon: const Icon(Icons.add, size: 16),
                label: Text(
                  'Add Validation',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0058FF),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                // Table header
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Entity Name',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Display Name',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Widget Type',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      SizedBox(
                        width: 80,
                        child: Text('Actions',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            )),
                      ),
                    ],
                  ),
                ),
                // Table rows
                ...uiStackRows.asMap().entries.map((entry) {
                  final index = entry.key;
                  final row = entry.value;
                  return Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Colors.grey[200]!),
                        bottom: BorderSide(color: Colors.grey[200]!),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['entityName'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['displayName'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['widgetType'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 80,
                          child: Row(
                            children: [
                              IconButton(
                                onPressed: () => _showUIControlsDialog(
                                    context,
                                    onAddRow,
                                    onEditRow,
                                    {'index': index, 'data': row}),
                                icon: const Icon(Icons.edit, size: 16),
                                color: Colors.blue,
                                constraints: const BoxConstraints(
                                    minWidth: 32, minHeight: 32),
                                padding: EdgeInsets.zero,
                              ),
                              IconButton(
                                onPressed: () => onDeleteRow(index),
                                icon:
                                    const Icon(Icons.delete_outline, size: 16),
                                color: Colors.red,
                                constraints: const BoxConstraints(
                                    minWidth: 32, minHeight: 32),
                                padding: EdgeInsets.zero,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Show UI Controls Configuration Dialog
  static void _showUIControlsDialog(
    BuildContext context,
    Function(
            String,
            String,
            String,
            String,
            String,
            int,
            String,
            String,
            String,
            List<Map<String, dynamic>>,
            List<Map<String, dynamic>>,
            List<Map<String, dynamic>>)
        onAddRow,
    Function(
            int,
            String,
            String,
            String,
            String,
            String,
            int,
            String,
            String,
            String,
            List<Map<String, dynamic>>,
            List<Map<String, dynamic>>,
            List<Map<String, dynamic>>)?
        onEditRow,
    Map<String, dynamic>? editData,
  ) {
    final isEditing = editData != null;
    final existingData = isEditing
        ? editData['data'] as Map<String, dynamic>
        : <String, dynamic>{};

    // Controllers for form fields
    final entityNameController =
        TextEditingController(text: existingData['entityName'] ?? '');
    final displayNameController =
        TextEditingController(text: existingData['displayName'] ?? '');
    final editingRightsController =
        TextEditingController(text: existingData['editingRights'] ?? '');
    final paginationController = TextEditingController(
        text: (existingData['pagination'] ?? 5).toString());
    final searchController =
        TextEditingController(text: existingData['search'] ?? '');
    final filtersController =
        TextEditingController(text: existingData['filters'] ?? '');

    // Dropdown values
    String selectedWidgetType = existingData['widgetType'] ?? 'Table';
    String selectedSelectionType = existingData['selectionType'] ?? 'Single';
    String selectedSort = existingData['sort'] ?? 'Shipping Date';
    String selectedSortOrder = 'Ascend';

    // Data filtering and sorting lists
    List<Map<String, dynamic>> dataFiltering =
        List<Map<String, dynamic>>.from(existingData['dataFiltering'] ?? []);
    List<Map<String, dynamic>> dataSorting =
        List<Map<String, dynamic>>.from(existingData['dataSorting'] ?? []);
    List<Map<String, dynamic>> conditionerColors =
        List<Map<String, dynamic>>.from(
            existingData['conditionerColors'] ?? []);

    // Validation function
    bool _areAllFieldsFilled() {
      return entityNameController.text.isNotEmpty &&
          displayNameController.text.isNotEmpty &&
          editingRightsController.text.isNotEmpty &&
          // paginationController.text.isNotEmpty &&
          searchController.text.isNotEmpty &&
          filtersController.text.isNotEmpty;
      // &&
      // selectedWidgetType.isNotEmpty &&
      // selectedSelectionType.isNotEmpty &&
      // selectedSort.isNotEmpty &&
      // dataFiltering.isNotEmpty &&
      // dataSorting.isNotEmpty;
      //  &&
      // conditionerColors.isNotEmpty;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: Container(
                width: 800,
                height: 600,
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'UI Controls Configuration',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyLarge(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.close),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Scrollable content
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // First row - Entity Name, Display Name, Widget Type
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Entity Name',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      TextField(
                                        controller: entityNameController,
                                        onChanged: (value) => setState(() {}),
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Display Name',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      TextField(
                                        controller: displayNameController,
                                        onChanged: (value) => setState(() {}),
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Widget Type',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      DropdownButtonFormField<String>(
                                        value: selectedWidgetType,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                        ),
                                        items: ['Table', 'List', 'Grid', 'Card']
                                            .map((String value) {
                                          return DropdownMenuItem<String>(
                                            value: value,
                                            child: Text(value,
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.grey,
                                                )),
                                          );
                                        }).toList(),
                                        onChanged: (String? newValue) {
                                          setState(() {
                                            selectedWidgetType = newValue!;
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Second row - Selection Type, Editing Rights, Widget Type
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Selection Type',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          Radio<String>(
                                            value: 'Single',
                                            groupValue: selectedSelectionType,
                                            onChanged: (String? value) {
                                              setState(() {
                                                selectedSelectionType = value!;
                                              });
                                            },
                                          ),
                                          Text(
                                            'Single',
                                            style: FontManager.getCustomStyle(
                                              fontSize:
                                                  ResponsiveFontSizes.bodySmall(
                                                      context),
                                              fontWeight: FontWeight.w400,
                                              fontFamily: FontManager
                                                  .fontFamilyTiemposText,
                                              color: Colors.black,
                                            ),
                                          ),
                                          const SizedBox(width: 16),
                                          Radio<String>(
                                            value: 'Multi',
                                            groupValue: selectedSelectionType,
                                            onChanged: (String? value) {
                                              setState(() {
                                                selectedSelectionType = value!;
                                              });
                                            },
                                          ),
                                          Text(
                                            'Multi',
                                            style: FontManager.getCustomStyle(
                                              fontSize:
                                                  ResponsiveFontSizes.bodySmall(
                                                      context),
                                              fontWeight: FontWeight.w400,
                                              fontFamily: FontManager
                                                  .fontFamilyTiemposText,
                                              color: Colors.black,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Editing Rights',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      TextField(
                                        controller: editingRightsController,
                                        onChanged: (value) => setState(() {}),
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Widget Type',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      DropdownButtonFormField<String>(
                                        value: selectedWidgetType,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                        ),
                                        items: ['Table', 'List', 'Grid', 'Card']
                                            .map((String value) {
                                          return DropdownMenuItem<String>(
                                            value: value,
                                            child: Text(value,
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.grey,
                                                )),
                                          );
                                        }).toList(),
                                        onChanged: (String? newValue) {
                                          setState(() {
                                            selectedWidgetType = newValue!;
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Third row - Pagination, Search, Filters
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Pagination',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      Row(
                                        children: [
                                          Expanded(
                                            child: TextField(
                                              controller: paginationController,
                                              onChanged: (value) =>
                                                  setState(() {}),
                                              decoration: InputDecoration(
                                                hintStyle:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w500,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.black,
                                                ),
                                                border: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                  borderSide: const BorderSide(
                                                      color: Color(0xFFE5E7EB)),
                                                ),
                                                enabledBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                  borderSide: const BorderSide(
                                                      color: Color(0xFFE5E7EB)),
                                                ),
                                                focusedBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                  borderSide: const BorderSide(
                                                      color: Color(0xFF0058FF)),
                                                ),
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                        horizontal: 12,
                                                        vertical: 8),
                                              ),
                                            ),
                                          ),
                                          const SizedBox(width: 8),
                                          IconButton(
                                            onPressed: () {
                                              int currentValue = int.tryParse(
                                                      paginationController
                                                          .text) ??
                                                  5;
                                              setState(() {
                                                paginationController.text =
                                                    (currentValue - 1)
                                                        .toString();
                                              });
                                            },
                                            icon: const Icon(Icons.remove,
                                                size: 16, color: Colors.grey),
                                          ),
                                          IconButton(
                                            onPressed: () {
                                              int currentValue = int.tryParse(
                                                      paginationController
                                                          .text) ??
                                                  5;
                                              setState(() {
                                                paginationController.text =
                                                    (currentValue + 1)
                                                        .toString();
                                              });
                                            },
                                            icon: const Icon(Icons.add,
                                                size: 16, color: Colors.grey),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Search',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      TextField(
                                        controller: searchController,
                                        onChanged: (value) => setState(() {}),
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Filters',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      TextField(
                                        controller: filtersController,
                                        onChanged: (value) => setState(() {}),
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Sort section
                            Text('Sort',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodySmall(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                )),
                            const SizedBox(height: 8),
                            DropdownButtonFormField<String>(
                              value: selectedSort,
                              decoration: InputDecoration(
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(6),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFE5E7EB)),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(6),
                                  borderSide: const BorderSide(
                                      color: Color(0xFFE5E7EB)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(6),
                                  borderSide: const BorderSide(
                                      color: Color(0xFF0058FF)),
                                ),
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 8),
                              ),
                              items: [
                                'Shipping Date',
                                'Customer Name',
                                'Order ID',
                                'Status'
                              ].map((String value) {
                                return DropdownMenuItem<String>(
                                  value: value,
                                  child: Text(
                                    value,
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w500,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    ),
                                  ),
                                );
                              }).toList(),
                              onChanged: (String? newValue) {
                                setState(() {
                                  selectedSort = newValue!;
                                });
                              },
                            ),
                            const SizedBox(height: 16),

                            // Data Filtering section
                            Text('Data Filtering',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodySmall(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                )),
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Color(0xFFfafafa),
                                border: Border.all(color: Colors.grey[300]!),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text('Attribute',
                                            style: FontManager.getCustomStyle(
                                              fontSize:
                                                  ResponsiveFontSizes.bodySmall(
                                                      context),
                                              fontWeight: FontWeight.w500,
                                              fontFamily: FontManager
                                                  .fontFamilyTiemposText,
                                              color: Colors.black,
                                            )),
                                        const SizedBox(height: 8),
                                        DropdownButtonFormField<String>(
                                          value: 'Shipping Date',
                                          decoration: InputDecoration(
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: const BorderSide(
                                                  color: Color(0xFFE5E7EB)),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: const BorderSide(
                                                  color: Color(0xFFE5E7EB)),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: const BorderSide(
                                                  color: Color(0xFF0058FF)),
                                            ),
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 12,
                                                    vertical: 8),
                                          ),
                                          items: [
                                            'Shipping Date',
                                            'Customer Name',
                                            'Status'
                                          ].map((String value) {
                                            return DropdownMenuItem<String>(
                                              value: value,
                                              child: Text(value,
                                                  style: FontManager
                                                      .getCustomStyle(
                                                    fontSize:
                                                        ResponsiveFontSizes
                                                            .bodySmall(context),
                                                    fontWeight: FontWeight.w400,
                                                    fontFamily: FontManager
                                                        .fontFamilyTiemposText,
                                                    color: Colors.grey,
                                                  )),
                                            );
                                          }).toList(),
                                          onChanged: (String? newValue) {},
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text('Value',
                                            style: FontManager.getCustomStyle(
                                              fontSize:
                                                  ResponsiveFontSizes.bodySmall(
                                                      context),
                                              fontWeight: FontWeight.w500,
                                              fontFamily: FontManager
                                                  .fontFamilyTiemposText,
                                              color: Colors.black,
                                            )),
                                        const SizedBox(height: 8),
                                        TextField(
                                          decoration: InputDecoration(
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                                borderSide: const BorderSide(
                                                    color: Color(0xFFE5E7EB)),
                                              ),
                                              enabledBorder: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                                borderSide: const BorderSide(
                                                    color: Color(0xFFE5E7EB)),
                                              ),
                                              focusedBorder: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                                borderSide: const BorderSide(
                                                    color: Color(0xFF0058FF)),
                                              ),
                                              contentPadding:
                                                  EdgeInsets.symmetric(
                                                      horizontal: 12,
                                                      vertical: 8),
                                              hintText: 'Current Date',
                                              hintStyle:
                                                  FontManager.getCustomStyle(
                                                fontSize: ResponsiveFontSizes
                                                    .bodySmall(context),
                                                fontWeight: FontWeight.w400,
                                                fontFamily: FontManager
                                                    .fontFamilyTiemposText,
                                                color: Colors.grey,
                                              )),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text('Operator',
                                            style: FontManager.getCustomStyle(
                                              fontSize:
                                                  ResponsiveFontSizes.bodySmall(
                                                      context),
                                              fontWeight: FontWeight.w500,
                                              fontFamily: FontManager
                                                  .fontFamilyTiemposText,
                                              color: Colors.black,
                                            )),
                                        const SizedBox(height: 8),
                                        DropdownButtonFormField<String>(
                                          value: '<',
                                          decoration: InputDecoration(
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: const BorderSide(
                                                  color: Color(0xFFE5E7EB)),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: const BorderSide(
                                                  color: Color(0xFFE5E7EB)),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: const BorderSide(
                                                  color: Color(0xFF0058FF)),
                                            ),
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 12,
                                                    vertical: 8),
                                          ),
                                          items: [
                                            '<',
                                            '>',
                                            '=',
                                            '!=',
                                            '<=',
                                            '>='
                                          ].map((String value) {
                                            return DropdownMenuItem<String>(
                                              value: value,
                                              child: Text(value,
                                                  style: FontManager
                                                      .getCustomStyle(
                                                    fontSize:
                                                        ResponsiveFontSizes
                                                            .bodySmall(context),
                                                    fontWeight: FontWeight.w400,
                                                    fontFamily: FontManager
                                                        .fontFamilyTiemposText,
                                                    color: Colors.black,
                                                  )),
                                            );
                                          }).toList(),
                                          onChanged: (String? newValue) {},
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Data Sorting section
                            Text('Data Sorting',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodySmall(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                )),
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Color(0xFFfafafa),
                                border: Border.all(color: Colors.grey[300]!),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text('Attribute',
                                            style: FontManager.getCustomStyle(
                                              fontSize:
                                                  ResponsiveFontSizes.bodySmall(
                                                      context),
                                              fontWeight: FontWeight.w500,
                                              fontFamily: FontManager
                                                  .fontFamilyTiemposText,
                                              color: Colors.black,
                                            )),
                                        const SizedBox(height: 8),
                                        DropdownButtonFormField<String>(
                                          value: 'Shipping Date',
                                          decoration: InputDecoration(
                                            border: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: const BorderSide(
                                                  color: Color(0xFFE5E7EB)),
                                            ),
                                            enabledBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: const BorderSide(
                                                  color: Color(0xFFE5E7EB)),
                                            ),
                                            focusedBorder: OutlineInputBorder(
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                              borderSide: const BorderSide(
                                                  color: Color(0xFF0058FF)),
                                            ),
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 12,
                                                    vertical: 8),
                                          ),
                                          items: [
                                            'Shipping Date',
                                            'Customer Name',
                                            'Status'
                                          ].map((String value) {
                                            return DropdownMenuItem<String>(
                                              value: value,
                                              child: Text(value,
                                                  style: FontManager
                                                      .getCustomStyle(
                                                    fontSize:
                                                        ResponsiveFontSizes
                                                            .bodySmall(context),
                                                    fontWeight: FontWeight.w400,
                                                    fontFamily: FontManager
                                                        .fontFamilyTiemposText,
                                                    color: Colors.black,
                                                  )),
                                            );
                                          }).toList(),
                                          onChanged: (String? newValue) {},
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          'Order',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          ),
                                        ),
                                        const SizedBox(height: 8),
                                        Row(
                                          children: [
                                            Radio<String>(
                                              value: 'Ascend',
                                              groupValue: selectedSortOrder,
                                              onChanged: (String? value) {
                                                setState(() {
                                                  selectedSortOrder = value!;
                                                });
                                              },
                                            ),
                                            Text('Ascend',
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.black,
                                                )),
                                            const SizedBox(width: 16),
                                            Radio<String>(
                                              value: 'Descend',
                                              groupValue: selectedSortOrder,
                                              onChanged: (String? value) {
                                                setState(() {
                                                  selectedSortOrder = value!;
                                                });
                                              },
                                            ),
                                            Text('Descend',
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.black,
                                                )),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Conditioner Colors section
                            Text('Conditioner Colours',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodySmall(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                )),
                            const SizedBox(height: 8),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Color(0xFFfafafa),
                                border: Border.all(color: Colors.grey[300]!),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text('Attribute',
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w500,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.black,
                                                )),
                                            const SizedBox(height: 8),
                                            DropdownButtonFormField<String>(
                                              value: 'Status',
                                              decoration: InputDecoration(
                                                border: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                  borderSide: const BorderSide(
                                                      color: Color(0xFFE5E7EB)),
                                                ),
                                                enabledBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                  borderSide: const BorderSide(
                                                      color: Color(0xFFE5E7EB)),
                                                ),
                                                focusedBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                  borderSide: const BorderSide(
                                                      color: Color(0xFF0058FF)),
                                                ),
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                        horizontal: 12,
                                                        vertical: 8),
                                              ),
                                              items: [
                                                'Status',
                                                'Priority',
                                                'Category'
                                              ].map((String value) {
                                                return DropdownMenuItem<String>(
                                                  value: value,
                                                  child: Text(value,
                                                      style: FontManager
                                                          .getCustomStyle(
                                                        fontSize:
                                                            ResponsiveFontSizes
                                                                .bodySmall(
                                                                    context),
                                                        fontWeight:
                                                            FontWeight.w500,
                                                        fontFamily: FontManager
                                                            .fontFamilyTiemposText,
                                                        color: Colors.grey,
                                                      )),
                                                );
                                              }).toList(),
                                              onChanged: (String? newValue) {},
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text('Value',
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w500,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.black,
                                                )),
                                            const SizedBox(height: 8),
                                            TextField(
                                              decoration: InputDecoration(
                                                  border: OutlineInputBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6),
                                                    borderSide:
                                                        const BorderSide(
                                                            color: Color(
                                                                0xFFE5E7EB)),
                                                  ),
                                                  enabledBorder:
                                                      OutlineInputBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6),
                                                    borderSide:
                                                        const BorderSide(
                                                            color: Color(
                                                                0xFFE5E7EB)),
                                                  ),
                                                  focusedBorder:
                                                      OutlineInputBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            6),
                                                    borderSide:
                                                        const BorderSide(
                                                            color: Color(
                                                                0xFF0058FF)),
                                                  ),
                                                  contentPadding:
                                                      EdgeInsets.symmetric(
                                                          horizontal: 12,
                                                          vertical: 8),
                                                  hintText: 'Delivered',
                                                  hintStyle: FontManager
                                                      .getCustomStyle(
                                                    fontSize:
                                                        ResponsiveFontSizes
                                                            .bodySmall(context),
                                                    fontWeight: FontWeight.w400,
                                                    fontFamily: FontManager
                                                        .fontFamilyTiemposText,
                                                    color: Colors.black,
                                                  )),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text('Operator',
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w500,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.black,
                                                )),
                                            const SizedBox(height: 8),
                                            DropdownButtonFormField<String>(
                                              value: '=',
                                              decoration: InputDecoration(
                                                border: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                  borderSide: const BorderSide(
                                                      color: Color(0xFFE5E7EB)),
                                                ),
                                                enabledBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                  borderSide: const BorderSide(
                                                      color: Color(0xFFE5E7EB)),
                                                ),
                                                focusedBorder:
                                                    OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(6),
                                                  borderSide: const BorderSide(
                                                      color: Color(0xFF0058FF)),
                                                ),
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                        horizontal: 12,
                                                        vertical: 8),
                                              ),
                                              items: [
                                                '=',
                                                '!=',
                                                '<',
                                                '>',
                                                '<=',
                                                '>='
                                              ].map((String value) {
                                                return DropdownMenuItem<String>(
                                                  value: value,
                                                  child: Text(value,
                                                      style: FontManager
                                                          .getCustomStyle(
                                                        fontSize:
                                                            ResponsiveFontSizes
                                                                .bodySmall(
                                                                    context),
                                                        fontWeight:
                                                            FontWeight.w400,
                                                        fontFamily: FontManager
                                                            .fontFamilyTiemposText,
                                                        color: Colors.grey,
                                                      )),
                                                );
                                              }).toList(),
                                              onChanged: (String? newValue) {},
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 16),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text('Fill Colour',
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w500,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.black,
                                                )),
                                            const SizedBox(height: 8),
                                            Container(
                                              height: 40,
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                    color: Colors.grey[300]!),
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    child: Padding(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 12),
                                                      child: Text('Fill Colour',
                                                          style: FontManager
                                                              .getCustomStyle(
                                                            fontSize:
                                                                ResponsiveFontSizes
                                                                    .bodySmall(
                                                                        context),
                                                            fontWeight:
                                                                FontWeight.w400,
                                                            fontFamily: FontManager
                                                                .fontFamilyTiemposText,
                                                            color: Colors.grey,
                                                          )),
                                                    ),
                                                  ),
                                                  Container(
                                                    width: 60,
                                                    height: 40,
                                                    decoration: BoxDecoration(
                                                      color: Colors.green,
                                                      borderRadius:
                                                          const BorderRadius
                                                              .only(
                                                        topRight:
                                                            Radius.circular(4),
                                                        bottomRight:
                                                            Radius.circular(4),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text('Font Colour',
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w500,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.black,
                                                )),
                                            const SizedBox(height: 8),
                                            Container(
                                              height: 40,
                                              decoration: BoxDecoration(
                                                border: Border.all(
                                                    color: Colors.grey[300]!),
                                                borderRadius:
                                                    BorderRadius.circular(4),
                                              ),
                                              child: Row(
                                                children: [
                                                  Expanded(
                                                    child: Padding(
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                          horizontal: 12),
                                                      child: Text(
                                                        'Font Colour',
                                                        style: FontManager
                                                            .getCustomStyle(
                                                          fontSize:
                                                              ResponsiveFontSizes
                                                                  .bodySmall(
                                                                      context),
                                                          fontWeight:
                                                              FontWeight.w400,
                                                          fontFamily: FontManager
                                                              .fontFamilyTiemposText,
                                                          color: Colors.grey,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  Container(
                                                    width: 60,
                                                    height: 40,
                                                    decoration: BoxDecoration(
                                                      color: Colors.black,
                                                      borderRadius:
                                                          const BorderRadius
                                                              .only(
                                                        topRight:
                                                            Radius.circular(4),
                                                        bottomRight:
                                                            Radius.circular(4),
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Footer buttons
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: Text('Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey,
                              )),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton(
                          onPressed: _areAllFieldsFilled()
                              ? () {
                                  if (isEditing && onEditRow != null) {
                                    onEditRow(
                                      editData['index'],
                                      entityNameController.text,
                                      displayNameController.text,
                                      selectedWidgetType,
                                      selectedSelectionType,
                                      editingRightsController.text,
                                      int.tryParse(paginationController.text) ??
                                          5,
                                      searchController.text,
                                      filtersController.text,
                                      selectedSort,
                                      dataFiltering,
                                      dataSorting,
                                      conditionerColors,
                                    );
                                  } else {
                                    onAddRow(
                                      entityNameController.text,
                                      displayNameController.text,
                                      selectedWidgetType,
                                      selectedSelectionType,
                                      editingRightsController.text,
                                      int.tryParse(paginationController.text) ??
                                          5,
                                      searchController.text,
                                      filtersController.text,
                                      selectedSort,
                                      dataFiltering,
                                      dataSorting,
                                      conditionerColors,
                                    );
                                  }
                                  Navigator.of(context).pop();
                                }
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _areAllFieldsFilled()
                                ? const Color(0xFF0058FF)
                                : Colors.grey,
                            foregroundColor: Colors.white,
                          ),
                          child: Text('Save Configuration',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              )),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Mapping Stack content with data table and dialog
  static Widget _buildMappingStackContentWithData(
    BuildContext context,
    SolutionCreationModel solution,
    List<Map<String, dynamic>> mappingStackRows, {
    required Function(String, String, String, String, String, String, String,
            String, String, String)
        onAddRow,
    required Function(int, String, String, String, String, String, String,
            String, String, String, String)
        onEditRow,
    required Function(int) onDeleteRow,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Data Mapping Configuration',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
              ElevatedButton.icon(
                onPressed: () =>
                    _showMappingDialog(context, onAddRow, null, null),
                icon: const Icon(Icons.add, size: 16),
                label: Text(
                  'Add Mapping',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0058FF),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                // Table header
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Source GO',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Source LO',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Source Type',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w400,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      SizedBox(
                          width: 80,
                          child: Text(
                            'Actions',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          )),
                    ],
                  ),
                ),
                // Table rows
                ...mappingStackRows.asMap().entries.map((entry) {
                  final index = entry.key;
                  final row = entry.value;
                  return Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Colors.grey[200]!),
                        bottom: BorderSide(color: Colors.grey[200]!),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['sourceGOName'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['sourceLOName'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['sourceType'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 80,
                          child: Row(
                            children: [
                              IconButton(
                                onPressed: () => _showMappingDialog(
                                    context,
                                    onAddRow,
                                    onEditRow,
                                    {'index': index, 'data': row}),
                                icon: const Icon(Icons.edit, size: 16),
                                color: Colors.blue,
                                constraints: const BoxConstraints(
                                    minWidth: 32, minHeight: 32),
                                padding: EdgeInsets.zero,
                              ),
                              IconButton(
                                onPressed: () => onDeleteRow(index),
                                icon:
                                    const Icon(Icons.delete_outline, size: 16),
                                color: Colors.red,
                                constraints: const BoxConstraints(
                                    minWidth: 32, minHeight: 32),
                                padding: EdgeInsets.zero,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Show Mapping Dialog
  static void _showMappingDialog(
    BuildContext context,
    Function(String, String, String, String, String, String, String, String,
            String, String)
        onAddRow,
    Function(int, String, String, String, String, String, String, String,
            String, String, String)?
        onEditRow,
    Map<String, dynamic>? editData,
  ) {
    final isEditing = editData != null;
    final existingData = isEditing
        ? editData['data'] as Map<String, dynamic>
        : <String, dynamic>{};

    // Controllers for form fields
    final sourceGONameController =
        TextEditingController(text: existingData['sourceGOName'] ?? '');
    final sourceLONameController =
        TextEditingController(text: existingData['sourceLOName'] ?? '');
    final sourceAttributeController =
        TextEditingController(text: existingData['sourceAttribute'] ?? '');
    final targetGONameController =
        TextEditingController(text: existingData['targetGOName'] ?? '');
    final targetLONameController =
        TextEditingController(text: existingData['targetLOName'] ?? '');
    final targetAttributeController =
        TextEditingController(text: existingData['targetAttribute'] ?? '');

    // Dropdown values
    String selectedSourceType = existingData['sourceType'] ?? 'OUTPUT';
    String selectedSourceEntity = existingData['sourceEntity'] ?? 'Customer';
    String selectedTargetType = existingData['targetType'] ?? 'INPUT';
    String selectedTargetEntity = existingData['targetEntity'] ?? 'Customer';

    //Validations
    bool _areAllFieldsFilled() {
      return sourceGONameController.text.isNotEmpty &&
          sourceLONameController.text.isNotEmpty &&
          sourceAttributeController.text.isNotEmpty &&
          targetGONameController.text.isNotEmpty &&
          targetLONameController.text.isNotEmpty &&
          targetAttributeController.text.isNotEmpty &&
          selectedSourceType.isNotEmpty &&
          selectedSourceEntity.isNotEmpty &&
          selectedTargetType.isNotEmpty &&
          selectedTargetEntity.isNotEmpty;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.439,
                height: MediaQuery.of(context).size.height * 0.651,
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Add Data Mapping',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyLarge(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.close),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Scrollable content
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Source Configuration
                            Text(
                              'Source Configuration',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Source GO Name and Source LO Name
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Source GO Name',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      TextField(
                                        controller: sourceGONameController,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                          hintText: 'CustomerManagement',
                                          hintStyle: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w400,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Source LO Name',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      TextField(
                                        controller: sourceLONameController,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                          hintText: 'CustomerOnboarding',
                                          hintStyle: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w400,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Source Type and Source Entity
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Source Type',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      DropdownButtonFormField<String>(
                                        value: selectedSourceType,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                                  horizontal: 12, vertical: 8),
                                        ),
                                        items: ['OUTPUT', 'INPUT']
                                            .map((String value) {
                                          return DropdownMenuItem<String>(
                                            value: value,
                                            child: Text(value,
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.grey,
                                                )),
                                          );
                                        }).toList(),
                                        onChanged: (String? newValue) {
                                          setState(() {
                                            selectedSourceType = newValue!;
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Source Entity',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      DropdownButtonFormField<String>(
                                        value: selectedSourceEntity,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                        ),
                                        items: [
                                          'Customer',
                                          'Order',
                                          'Product',
                                          'User'
                                        ].map((String value) {
                                          return DropdownMenuItem<String>(
                                            value: value,
                                            child: Text(value,
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.grey,
                                                )),
                                          );
                                        }).toList(),
                                        onChanged: (String? newValue) {
                                          setState(() {
                                            selectedSourceEntity = newValue!;
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Source Attribute
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Source Attribute',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w500,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    )),
                                const SizedBox(height: 8),
                                TextField(
                                  controller: sourceAttributeController,
                                  decoration: InputDecoration(
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: const BorderSide(
                                          color: Color(0xFFE5E7EB)),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: const BorderSide(
                                          color: Color(0xFFE5E7EB)),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: const BorderSide(
                                          color: Color(0xFF0058FF)),
                                    ),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    hintText: 'customer_id',
                                    hintStyle: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),

                            // Target Configuration
                            Text(
                              'Target Configuration',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(height: 16),

                            // Target GO Name and Target LO Name
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Target GO Name',
                                        style: FontManager.getCustomStyle(
                                          fontSize:
                                              ResponsiveFontSizes.bodySmall(
                                                  context),
                                          fontWeight: FontWeight.w500,
                                          fontFamily:
                                              FontManager.fontFamilyTiemposText,
                                          color: Colors.black,
                                        ),
                                      ),
                                      const SizedBox(height: 8),
                                      TextField(
                                        controller: targetGONameController,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                          hintText: 'CustomerManagement',
                                          hintStyle: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w400,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Target LO Name',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      TextField(
                                        controller: targetLONameController,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                          hintText: 'SendWelcomeEmail',
                                          hintStyle: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w400,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Target Type and Target Entity
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Target Type',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      DropdownButtonFormField<String>(
                                        value: selectedTargetType,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                        ),
                                        items: ['INPUT', 'OUTPUT']
                                            .map((String value) {
                                          return DropdownMenuItem<String>(
                                            value: value,
                                            child: Text(value,
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.grey,
                                                )),
                                          );
                                        }).toList(),
                                        onChanged: (String? newValue) {
                                          setState(() {
                                            selectedTargetType = newValue!;
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Target Entity',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      DropdownButtonFormField<String>(
                                        value: selectedTargetEntity,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          enabledBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          focusedBorder: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFF0058FF)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                        ),
                                        items: [
                                          'Customer',
                                          'Order',
                                          'Product',
                                          'User'
                                        ].map((String value) {
                                          return DropdownMenuItem<String>(
                                            value: value,
                                            child: Text(value,
                                                style:
                                                    FontManager.getCustomStyle(
                                                  fontSize: ResponsiveFontSizes
                                                      .bodySmall(context),
                                                  fontWeight: FontWeight.w400,
                                                  fontFamily: FontManager
                                                      .fontFamilyTiemposText,
                                                  color: Colors.grey,
                                                )),
                                          );
                                        }).toList(),
                                        onChanged: (String? newValue) {
                                          setState(() {
                                            selectedTargetEntity = newValue!;
                                          });
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Target Attribute
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Target Attribute',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w500,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    )),
                                const SizedBox(height: 8),
                                TextField(
                                  controller: targetAttributeController,
                                  decoration: InputDecoration(
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: const BorderSide(
                                          color: Color(0xFFE5E7EB)),
                                    ),
                                    enabledBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: const BorderSide(
                                          color: Color(0xFFE5E7EB)),
                                    ),
                                    focusedBorder: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: const BorderSide(
                                          color: Color(0xFF0058FF)),
                                    ),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    hintText: 'customer_id',
                                    hintStyle: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Footer buttons
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: Text('Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey,
                              )),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton(
                          onPressed: _areAllFieldsFilled()
                              ? () {
                                  if (isEditing && onEditRow != null) {
                                    onEditRow(
                                      editData['index'],
                                      sourceGONameController.text,
                                      sourceLONameController.text,
                                      selectedSourceType,
                                      selectedSourceEntity,
                                      sourceAttributeController.text,
                                      targetGONameController.text,
                                      targetLONameController.text,
                                      selectedTargetType,
                                      selectedTargetEntity,
                                      targetAttributeController.text,
                                    );
                                  } else {
                                    onAddRow(
                                      sourceGONameController.text,
                                      sourceLONameController.text,
                                      selectedSourceType,
                                      selectedSourceEntity,
                                      sourceAttributeController.text,
                                      targetGONameController.text,
                                      targetLONameController.text,
                                      selectedTargetType,
                                      selectedTargetEntity,
                                      targetAttributeController.text,
                                    );
                                  }
                                  Navigator.of(context).pop();
                                }
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0058FF),
                            foregroundColor: Colors.white,
                          ),
                          child: Text('Apply This',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              )),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Mapping Stack content
  static Widget _buildMappingStackContent(
      BuildContext context, SolutionCreationModel solution) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Mapping Stack Configuration',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyLarge(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            'Mappings: ${solution.mappingStack?.mappingCount ?? 3}',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[600],
            ),
          ),
          Text(
            'Status: ${solution.mappingStack?.status ?? 'Configured'}',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  /// Nested Function Pathways content with data table and dialog
  static Widget _buildNestedFunctionPathwaysContentWithData(
    BuildContext context,
    SolutionCreationModel solution,
    List<Map<String, dynamic>> nestedFunctionPathwaysRows, {
    required Function(String, String, String, String) onAddRow,
    required Function(int, String, String, String, String) onEditRow,
    required Function(int) onDeleteRow,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Data Mapping Configuration',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
              ElevatedButton.icon(
                onPressed: () =>
                    _showNestedFunctionDialog(context, onAddRow, null, null),
                icon: const Icon(Icons.add, size: 16),
                label: Text(
                  'Add Mapping',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0058FF),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                // Table header
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: Text(
                          'Function ID',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Function Name',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Function Type',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                          flex: 1,
                          child: Text(
                            'Actions',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          )),
                    ],
                  ),
                ),
                // Table rows
                ...nestedFunctionPathwaysRows.asMap().entries.map((entry) {
                  final index = entry.key;
                  final row = entry.value;
                  return Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Colors.grey[200]!),
                        bottom: BorderSide(color: Colors.grey[200]!),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 1,
                          child: Text(
                            row['functionId'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['functionName'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['functionType'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Row(
                            children: [
                              IconButton(
                                onPressed: () => _showNestedFunctionDialog(
                                    context,
                                    onAddRow,
                                    onEditRow,
                                    {'index': index, 'data': row}),
                                icon: const Icon(Icons.edit, size: 16),
                                color: Colors.blue,
                                constraints: const BoxConstraints(
                                    minWidth: 32, minHeight: 32),
                                padding: EdgeInsets.zero,
                              ),
                              IconButton(
                                onPressed: () => onDeleteRow(index),
                                icon:
                                    const Icon(Icons.delete_outline, size: 16),
                                color: Colors.red,
                                constraints: const BoxConstraints(
                                    minWidth: 32, minHeight: 32),
                                padding: EdgeInsets.zero,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
        ],
      ),
    );
  }
   //Nested Pathway 
  static void _showNestedFunctionDialog(
    BuildContext context,
    Function(String, String, String, String) onAddRow,
    Function(int, String, String, String, String)? onEditRow,
    Map<String, dynamic>? editData,
  ) {
    final isEditing = editData != null;
    final existingData = isEditing
        ? editData['data'] as Map<String, dynamic>
        : <String, dynamic>{};

    // Controllers for form fields
    final functionIdController =
        TextEditingController(text: existingData['functionId'] ?? '');
    final functionNameController =
        TextEditingController(text: existingData['functionName'] ?? '');
    final descriptionController =
        TextEditingController(text: existingData['description'] ?? '');

    // Dropdown value
    String selectedFunctionType = existingData['functionType'] ?? 'send email';

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Function to check if all required fields are filled
            bool allFieldsValid() {
              return functionIdController.text.isNotEmpty &&
                  functionNameController.text.isNotEmpty ;
                  // descriptionController.text.isNotEmpty;
            }

            // Function to update button state
            void updateButtonState() {
              setState(() {});
            }

            // Add listeners to controllers to update button state
            functionIdController.addListener(updateButtonState);
            functionNameController.addListener(updateButtonState);
            descriptionController.addListener(updateButtonState);

            return Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.366,
                height: MediaQuery.of(context).size.height * 0.52,
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Nested Function',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyLarge(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.close),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Scrollable content
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Function ID and Function Name
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Function ID',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      TextField(
                                        controller: functionIdController,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                          hintText: 'NF1',
                                          hintStyle: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w400,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text('Function Name',
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w500,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.black,
                                          )),
                                      const SizedBox(height: 8),
                                      TextField(
                                        controller: functionNameController,
                                        decoration: InputDecoration(
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            borderSide: const BorderSide(
                                                color: Color(0xFFE5E7EB)),
                                          ),
                                          contentPadding: EdgeInsets.symmetric(
                                              horizontal: 12, vertical: 8),
                                          hintText: 'function_name',
                                          hintStyle: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w400,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.grey,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Function Type
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Function Type',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w500,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    )),
                                const SizedBox(height: 8),
                                DropdownButtonFormField<String>(
                                  value: selectedFunctionType,
                                  decoration: InputDecoration(
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: const BorderSide(
                                          color: Color(0xFFE5E7EB)),
                                    ),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                  ),
                                  items: [
                                    'send email',
                                    'conditional_assignment',
                                    'create_record',
                                    'update_record',
                                    'delete_record',
                                    'validate_data',
                                    'transform_data',
                                    'send_notification'
                                  ].map((String value) {
                                    return DropdownMenuItem<String>(
                                      value: value,
                                      child: Text(value,
                                          style: FontManager.getCustomStyle(
                                            fontSize:
                                                ResponsiveFontSizes.bodySmall(
                                                    context),
                                            fontWeight: FontWeight.w400,
                                            fontFamily: FontManager
                                                .fontFamilyTiemposText,
                                            color: Colors.grey,
                                          )),
                                    );
                                  }).toList(),
                                  onChanged: (String? newValue) {
                                    setState(() {
                                      selectedFunctionType = newValue!;
                                    });
                                  },
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Description
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Description',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w500,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    )),
                                const SizedBox(height: 8),
                                TextField(
                                  controller: descriptionController,
                                  maxLines: 4,
                                  decoration: InputDecoration(
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: const BorderSide(
                                          color: Color(0xFFE5E7EB)),
                                    ),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    hintText: 'Description',
                                    hintStyle: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Footer buttons
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: Text('Cancel',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.grey[600],
                              )),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton(
                          onPressed: allFieldsValid()
                              ? () {
                                  if (isEditing && onEditRow != null) {
                                    onEditRow(
                                      editData['index'],
                                      functionIdController.text,
                                      functionNameController.text,
                                      selectedFunctionType,
                                      descriptionController.text,
                                    );
                                  } else {
                                    onAddRow(
                                      functionIdController.text,
                                      functionNameController.text,
                                      selectedFunctionType,
                                      descriptionController.text,
                                    );
                                  }
                                  Navigator.of(context).pop();
                                }
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: allFieldsValid()
                                ? const Color(0xFF0058FF)
                                : Colors.grey,
                            foregroundColor: Colors.white,
                          ),
                          child: Text('Apply This',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              )),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
 
 
    /// Execution Pathway content with data table and dialog
  static Widget _buildExecutionPathwayContentWithData(
    BuildContext context,
    SolutionCreationModel solution,
    List<Map<String, dynamic>> executionPathwayRows, {
    required Function(String, String, String) onAddRow,
    required Function(int, String, String, String) onEditRow,
    required Function(int) onDeleteRow,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header section
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Execution Routing Configuration',
                    style: FontManager.getCustomStyle(
                      fontSize: ResponsiveFontSizes.bodyMedium(context),
                      fontWeight: FontWeight.w500,
                      fontFamily: FontManager.fontFamilyTiemposText,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
              ElevatedButton.icon(
                onPressed: () =>
                    _showExecutionPathwayDialog(context, onAddRow, null, null),
                icon: const Icon(Icons.add, size: 16),
                label: Text(
                  'Add Route',
                  style: FontManager.getCustomStyle(
                    fontSize: ResponsiveFontSizes.bodySmall(context),
                    fontWeight: FontWeight.w500,
                    fontFamily: FontManager.fontFamilyTiemposText,
                    color: Colors.white,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF0058FF),
                  foregroundColor: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(6),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Table
          Container(
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                // Table header
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Condition',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          'Function Name',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 3,
                        child: Text(
                          'Description',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodySmall(context),
                            fontWeight: FontWeight.w500,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                      ),
                      Expanded(
                          flex: 1,
                          child: Text(
                            'Actions',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          )),
                    ],
                  ),
                ),
                // Table rows
                ...executionPathwayRows.asMap().entries.map((entry) {
                  final index = entry.key;
                  final row = entry.value;
                  return Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Colors.grey[200]!),
                        bottom: BorderSide(color: Colors.grey[200]!),
                      ),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['condition'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: Text(
                            row['functionName'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 3,
                          child: Text(
                            row['description'] ?? '',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodySmall(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Row(
                            children: [
                              IconButton(
                                onPressed: () => _showExecutionPathwayDialog(
                                    context,
                                    onAddRow,
                                    onEditRow,
                                    {'index': index, 'data': row}),
                                icon: const Icon(Icons.edit, size: 16),
                                color: Colors.blue,
                                constraints: const BoxConstraints(
                                    minWidth: 32, minHeight: 32),
                                padding: EdgeInsets.zero,
                              ),
                              IconButton(
                                onPressed: () => onDeleteRow(index),
                                icon:
                                    const Icon(Icons.delete_outline, size: 16),
                                color: Colors.red,
                                constraints: const BoxConstraints(
                                    minWidth: 32, minHeight: 32),
                                padding: EdgeInsets.zero,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }).toList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Show Execution Pathway Dialog
 static void _showExecutionPathwayDialog(
    BuildContext context,
    Function(String, String, String) onAddRow,
    Function(int, String, String, String)? onEditRow,
    Map<String, dynamic>? editData,
  ) {
    final isEditing = editData != null;
    final existingData = isEditing
        ? editData['data'] as Map<String, dynamic>
        : <String, dynamic>{};

    // Controllers for form fields
    final conditionController =
        TextEditingController(text: existingData['condition'] ?? '');
    final functionNameController =
        TextEditingController(text: existingData['functionName'] ?? '');
    final descriptionController =
        TextEditingController(text: existingData['description'] ?? '');

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Function to check if all fields are filled
            bool allFieldsValid() {
              return conditionController.text.isNotEmpty &&
                  functionNameController.text.isNotEmpty &&
                  descriptionController.text.isNotEmpty;
            }

            // Function to update button state
            void updateButtonState() {
              setState(() {});
            }

            // Add listeners to controllers to update button state
            conditionController.addListener(updateButtonState);
            functionNameController.addListener(updateButtonState);
            descriptionController.addListener(updateButtonState);

            return Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.366,
                height: MediaQuery.of(context).size.height * 0.52, 
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Execution Routing Configuration',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyLarge(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.close),
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Scrollable content
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Condition
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Condition',
                                  style: FontManager.getCustomStyle(
                                    fontSize:
                                        ResponsiveFontSizes.bodySmall(context),
                                    fontWeight: FontWeight.w500,
                                    fontFamily:
                                        FontManager.fontFamilyTiemposText,
                                    color: Colors.black,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                TextField(
                                  controller: conditionController,
                                  decoration: InputDecoration(
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: const BorderSide(
                                          color: Color(0xFFE5E7EB)),
                                    ),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    hintText:
                                        'Customer.verification_status = "verified"',
                                    hintStyle: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Function Name
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Function Name',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w500,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    )),
                                const SizedBox(height: 8),
                                TextField(
                                  controller: functionNameController,
                                  decoration: InputDecoration(
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: const BorderSide(
                                          color: Color(0xFFE5E7EB)),
                                    ),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    hintText: 'SendWelcomeEmail',
                                    hintStyle: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Description
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Description',
                                    style: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w500,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.black,
                                    )),
                                const SizedBox(height: 8),
                                TextField(
                                  controller: descriptionController,
                                  maxLines: 4,
                                  decoration: InputDecoration(
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(6),
                                      borderSide: const BorderSide(
                                          color: Color(0xFFE5E7EB)),
                                    ),
                                    contentPadding: EdgeInsets.symmetric(
                                        horizontal: 12, vertical: 8),
                                    hintText:
                                        'Route to welcome email after successful verification',
                                    hintStyle: FontManager.getCustomStyle(
                                      fontSize: ResponsiveFontSizes.bodySmall(
                                          context),
                                      fontWeight: FontWeight.w400,
                                      fontFamily:
                                          FontManager.fontFamilyTiemposText,
                                      color: Colors.grey,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Footer buttons
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('Cancel'),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton(
                          onPressed: allFieldsValid()
                              ? () {
                                  if (isEditing && onEditRow != null) {
                                    onEditRow(
                                      editData['index'],
                                      conditionController.text,
                                      functionNameController.text,
                                      descriptionController.text,
                                    );
                                  } else {
                                    onAddRow(
                                      conditionController.text,
                                      functionNameController.text,
                                      descriptionController.text,
                                    );
                                  }
                                  Navigator.of(context).pop();
                                }
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: allFieldsValid()
                                ? const Color(0xFF0058FF)
                                : Colors.grey,
                            foregroundColor: Colors.white,
                          ),
                          child: Text('Apply This',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodyMedium(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              )),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Show Assign Rights Dialog
  static void _showAssignRightsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.366,
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Text(
                      'Add Input Configuration',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyLarge(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const Spacer(),
                    _HoverButton(
                      text: 'Add More Role',
                      hoverText: 'Add Input',
                      onPressed: () {},
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Select Role
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Select Role',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: const Color(0xFFE5E7EB)),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        children: [
                          Text(
                            'Customer',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                          const Spacer(),
                          const Icon(Icons.keyboard_arrow_down,
                              color: Colors.grey),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Select Rights
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Select Rights',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        _buildCheckbox('View'),
                        const SizedBox(width: 16),
                        _buildCheckbox('Read'),
                        const SizedBox(width: 16),
                        _buildCheckbox('Execution'),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        'Cancel',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    _HoverButton(
                      text: 'Create',
                      hoverText: 'Add Input',
                      onPressed: () => Navigator.of(context).pop(),
                      isPrimary: true,
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Show Attributes Dialog
  static void _showAttributesDialog(BuildContext context,
      {Function(String, String, bool)? onApply,
      String? entity,
      String? attribute,
      bool? required}) {
    final TextEditingController entityController =
        TextEditingController(text: entity ?? 'Customer');
    final TextEditingController attributeController =
        TextEditingController(text: attribute ?? 'Customer ID');
    bool isRequired = required ?? true;

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Function to check if all fields are valid
            bool areAllFieldsValid() {
              return entityController.text.trim().isNotEmpty &&
                  attributeController.text.trim().isNotEmpty;
            }

            return Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.366,
                padding: const EdgeInsets.all(20),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        Text(
                          'Output Attributes',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyLarge(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.close),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Entity and Attribute Fields in a Row
                    Row(
                      children: [
                        // Entity Field
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Entity',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                              const SizedBox(height: 8),
                              TextField(
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodySmall(context),
                                  fontWeight: FontWeight.w400,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                                controller: entityController,
                                onChanged: (value) {
                                  // Trigger rebuild when text changes
                                  setState(() {});
                                },
                                decoration: InputDecoration(
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(6),
                                    borderSide: const BorderSide(
                                        color: Color(0xFFE5E7EB)),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(6),
                                    borderSide: const BorderSide(
                                        color: Color(0xFFE5E7EB)),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(6),
                                    borderSide: const BorderSide(
                                        color: Color(0xFF0058FF)),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8),
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(width: 16), // Space between fields

                        // Attribute Field
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Attribute',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                              const SizedBox(height: 8),
                              TextField(
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodySmall(context),
                                  fontWeight: FontWeight.w400,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                                controller: attributeController,
                                onChanged: (value) {
                                  // Trigger rebuild when text changes
                                  setState(() {});
                                },
                                decoration: InputDecoration(
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(6),
                                    borderSide: const BorderSide(
                                        color: Color(0xFFE5E7EB)),
                                  ),
                                  enabledBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(6),
                                    borderSide: const BorderSide(
                                        color: Color(0xFFE5E7EB)),
                                  ),
                                  focusedBorder: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(6),
                                    borderSide: const BorderSide(
                                        color: Color(0xFF0058FF)),
                                  ),
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Required Field
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Required',
                                style: FontManager.getCustomStyle(
                                  fontSize:
                                      ResponsiveFontSizes.bodyMedium(context),
                                  fontWeight: FontWeight.w500,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: Colors.black,
                                ),
                              ),
                              const SizedBox(height: 8),
                              GestureDetector(
                                onTap: () =>
                                    setState(() => isRequired = !isRequired),
                                child: Container(
                                  width: double.infinity,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 12, vertical: 8),
                                  decoration: BoxDecoration(
                                    border: Border.all(
                                        color: const Color(0xFFE5E7EB)),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Row(
                                    children: [
                                      Text(
                                        isRequired.toString(),
                                        style: FontManager.getCustomStyle(
                                          fontSize:
                                              ResponsiveFontSizes.bodySmall(
                                                  context),
                                          fontWeight: FontWeight.w400,
                                          fontFamily:
                                              FontManager.fontFamilyTiemposText,
                                          color: Colors.black,
                                        ),
                                      ),
                                      const Spacer(),
                                      const Icon(Icons.keyboard_arrow_down,
                                          color: Colors.grey),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: SizedBox(),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Add Attributes Button
                    _HoverButton(
                      text: '+ Add Attributes',
                      hoverText: '+ Add Value',
                      onPressed: () {},
                    ),

                    const SizedBox(height: 24),

                    // Buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: Text(
                            'Cancel',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton(
                          onPressed: areAllFieldsValid()
                              ? () {
                                  // Get the form data
                                  final entity = entityController.text.trim();
                                  final attribute =
                                      attributeController.text.trim();

                                  Navigator.of(context).pop();
                                  // Call the callback with the new data
                                  onApply?.call(entity, attribute, isRequired);
                                }
                              : null, // Disable button when fields are empty
                          style: ElevatedButton.styleFrom(
                            backgroundColor: areAllFieldsValid()
                                ? const Color(0xFF0058FF)
                                : Colors.grey[300], // Grey when disabled
                            foregroundColor: areAllFieldsValid()
                                ? Colors.white
                                : Colors.grey[600], // Grey text when disabled
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 8),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                          child: Text(
                            'Apply This',
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: areAllFieldsValid()
                                  ? Colors.white
                                  : Colors.grey[600],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  /// Show Edit Attribute Dialog
  static void _showEditAttributeDialog(
      BuildContext context, String entity, String attribute, bool required) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.366,
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Text(
                      'Edit Output Attributes',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyLarge(context),
                        fontWeight: FontWeight.w600,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const Spacer(),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Entity Field (prefilled)
                _buildDialogField(context, 'Entity', entity),

                const SizedBox(height: 16),

                // Attribute Field (prefilled)
                _buildDialogField(context, 'Attribute', attribute),

                const SizedBox(height: 16),

                // Required Field (prefilled)
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Required',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: Colors.black,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: const Color(0xFFE5E7EB)),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        children: [
                          Text(
                            required.toString(),
                            style: FontManager.getCustomStyle(
                              fontSize: ResponsiveFontSizes.bodyMedium(context),
                              fontWeight: FontWeight.w400,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                          ),
                          const Spacer(),
                          const Icon(Icons.keyboard_arrow_down,
                              color: Colors.grey),
                        ],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        'Cancel',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    ElevatedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0058FF),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      child: Text(
                        'Apply',
                        style: FontManager.getCustomStyle(
                          fontSize: ResponsiveFontSizes.bodyMedium(context),
                          fontWeight: FontWeight.w500,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Show Delete Confirmation
  static void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            'Delete Attribute',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyLarge(context),
              fontWeight: FontWeight.w600,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          content: Text(
            'Are you sure you want to delete this attribute?',
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Cancel',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.grey[600],
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
              child: Text(
                'Delete',
                style: FontManager.getCustomStyle(
                  fontSize: ResponsiveFontSizes.bodyMedium(context),
                  fontWeight: FontWeight.w500,
                  fontFamily: FontManager.fontFamilyTiemposText,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Build dialog field
  static Widget _buildDialogField(
      BuildContext context, String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFE5E7EB)),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Text(
            value,
            style: FontManager.getCustomStyle(
              fontSize: ResponsiveFontSizes.bodyMedium(context),
              fontWeight: FontWeight.w400,
              fontFamily: FontManager.fontFamilyTiemposText,
              color: Colors.black,
            ),
          ),
        ),
      ],
    );
  }

  /// Build checkbox
  static Widget _buildCheckbox(String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFE5E7EB)),
            borderRadius: BorderRadius.circular(3),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  // /// Show validation rules dialog
  // static void _showValidationRulesDialog(
  //   BuildContext context, {
  //   String? attribute,
  //   String? validationFunction,
  //   String? successCondition,
  //   String? failureCondition,
  //   String? successMessage,
  //   String? failureMessage,
  //   Function(String, String, String, String, String, String)? onApply,
  // }) {
  //   final attributeController = TextEditingController(text: attribute ?? '');
  //   final validationFunctionController =
  //       TextEditingController(text: validationFunction ?? '');
  //   final successConditionController =
  //       TextEditingController(text: successCondition ?? '');
  //   final failureConditionController =
  //       TextEditingController(text: failureCondition ?? '');
  //   final successMessageController =
  //       TextEditingController(text: successMessage ?? '');
  //   final failureMessageController =
  //       TextEditingController(text: failureMessage ?? '');

  //   showDialog(
  //     context: context,
  //     builder: (BuildContext context) {
  //       return Dialog(
  //         shape:
  //             RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
  //         child: Container(
  //           width: 600,
  //           padding: const EdgeInsets.all(24),
  //           child: Column(
  //             mainAxisSize: MainAxisSize.min,
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               // Header
  //               Row(
  //                 children: [
  //                   Text(
  //                     'Validation Rules',
  //                     style: FontManager.getCustomStyle(
  //                       fontSize: ResponsiveFontSizes.bodyLarge(context),
  //                       fontWeight: FontWeight.w600,
  //                       fontFamily: FontManager.fontFamilyTiemposText,
  //                       color: Colors.black,
  //                     ),
  //                   ),
  //                   const Spacer(),
  //                   IconButton(
  //                     onPressed: () => Navigator.of(context).pop(),
  //                     icon: const Icon(Icons.close),
  //                     padding: EdgeInsets.zero,
  //                     constraints: const BoxConstraints(),
  //                   ),
  //                 ],
  //               ),

  //               const SizedBox(height: 24),

  //               // Form fields in 2x3 grid
  //               Row(
  //                 crossAxisAlignment: CrossAxisAlignment.start,
  //                 children: [
  //                   // Left column
  //                   Expanded(
  //                     child: Column(
  //                       children: [
  //                         // Attribute field
  //                         _buildValidationDialogField(
  //                           context,
  //                           'Attribute',
  //                           attributeController,
  //                           'Field to validate (e.g., Customer.email,Order.quantity)',
  //                         ),
  //                         const SizedBox(height: 16),

  //                         // Success Condition field
  //                         _buildValidationDialogField(
  //                           context,
  //                           'Success Condition',
  //                           successConditionController,
  //                           'Condition for valid input (e.g., age >= 18, phone LENGTH == 10)',
  //                         ),
  //                         const SizedBox(height: 16),

  //                         // Success Message field
  //                         _buildValidationDialogField(
  //                           context,
  //                           'Success Message',
  //                           successMessageController,
  //                           'Message shown when validation passes (e.g., Valid age, Phone number correct)',
  //                         ),
  //                       ],
  //                     ),
  //                   ),

  //                   const SizedBox(width: 16),

  //                   // Right column
  //                   Expanded(
  //                     child: Column(
  //                       children: [
  //                         // Validation Function field
  //                         _buildValidationDialogField(
  //                           context,
  //                           'Validation Function',
  //                           validationFunctionController,
  //                           'Validation rule to apply (e.g., validate_email_format, range_check)',
  //                         ),
  //                         const SizedBox(height: 16),

  //                         // Failure Condition field
  //                         _buildValidationDialogField(
  //                           context,
  //                           'Failure Condition',
  //                           failureConditionController,
  //                           'Condition for invalid input (e.g., endDate<startDate, name IS_NULL)',
  //                         ),
  //                         const SizedBox(height: 16),

  //                         // Failure Message field
  //                         _buildValidationDialogField(
  //                           context,
  //                           'Failure Message',
  //                           failureMessageController,
  //                           'Error message shown when validation fails (e.g., Invalid email format, Name is required)',
  //                         ),
  //                       ],
  //                     ),
  //                   ),
  //                 ],
  //               ),

  //               const SizedBox(height: 32),

  //               // Buttons
  //               Row(
  //                 mainAxisAlignment: MainAxisAlignment.end,
  //                 children: [
  //                   TextButton(
  //                     onPressed: () => Navigator.of(context).pop(),
  //                     child: Text(
  //                       'Cancel',
  //                       style: FontManager.getCustomStyle(
  //                         fontSize: 14,
  //                         fontWeight: FontWeight.w500,
  //                         fontFamily: FontManager.fontFamilyTiemposText,
  //                         color: Colors.grey[600],
  //                       ),
  //                     ),
  //                   ),
  //                   const SizedBox(width: 12),
  //                   ElevatedButton(
  //                     onPressed: () {
  //                       onApply?.call(
  //                         attributeController.text,
  //                         validationFunctionController.text,
  //                         successConditionController.text,
  //                         failureConditionController.text,
  //                         successMessageController.text,
  //                         failureMessageController.text,
  //                       );
  //                       Navigator.of(context).pop();
  //                     },
  //                     style: ElevatedButton.styleFrom(
  //                       backgroundColor: const Color(0xFF0058FF),
  //                       foregroundColor: Colors.white,
  //                       padding: const EdgeInsets.symmetric(
  //                           horizontal: 24, vertical: 12),
  //                       shape: RoundedRectangleBorder(
  //                         borderRadius: BorderRadius.circular(6),
  //                       ),
  //                     ),
  //                     child: Text(
  //                       'Apply This',
  //                       style: FontManager.getCustomStyle(
  //                         fontSize: 14,
  //                         fontWeight: FontWeight.w500,
  //                         fontFamily: FontManager.fontFamilyTiemposText,
  //                         color: Colors.white,
  //                       ),
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //             ],
  //           ),
  //         ),
  //       );
  //     },
  //   );
  // }

  // /// Build validation dialog field with tooltip
  // static Widget _buildValidationDialogField(
  //   BuildContext context,
  //   String label,
  //   TextEditingController controller,
  //   String tooltipText,
  // ) {
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       Row(
  //         mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //         children: [
  //           Text(
  //             label,
  //             style: FontManager.getCustomStyle(
  //               fontSize: ResponsiveFontSizes.bodySmall(context),
  //               fontWeight: FontWeight.w500,
  //               fontFamily: FontManager.fontFamilyTiemposText,
  //               color: Colors.black,
  //             ),
  //           ),
  //           Tooltip(
  //             padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
  //             message: tooltipText,
  //             decoration: BoxDecoration(
  //               color: const Color(0xFFeff6ff),
  //               borderRadius: BorderRadius.circular(6),
  //             ),
  //             textStyle: FontManager.getCustomStyle(
  //               fontSize: ResponsiveFontSizes.bodySmall(context),
  //               fontWeight: FontWeight.w500,
  //               fontFamily: FontManager.fontFamilyTiemposText,
  //               color: Colors.black,
  //             ),
  //             // preferBelow: false,
  //             child: Container(
  //               width: 16,
  //               height: 16,
  //               decoration: const BoxDecoration(
  //                 // color: Color(0xFF0058FF),
  //                 shape: BoxShape.circle,
  //               ),
  //               child: const Icon(
  //                 Icons.info_outline_rounded,
  //                 size: 18,
  //                 color: Color(0xFF0058FF),
  //               ),
  //             ),
  //           ),
  //         ],
  //       ),
  //       const SizedBox(height: 8),
  //       TextField(
  //         controller: controller,
  //         decoration: InputDecoration(
  //           border: OutlineInputBorder(
  //             borderRadius: BorderRadius.circular(6),
  //             borderSide: const BorderSide(color: Color(0xFFE5E7EB)),
  //           ),
  //           enabledBorder: OutlineInputBorder(
  //             borderRadius: BorderRadius.circular(6),
  //             borderSide: const BorderSide(color: Color(0xFFE5E7EB)),
  //           ),
  //           focusedBorder: OutlineInputBorder(
  //             borderRadius: BorderRadius.circular(6),
  //             borderSide: const BorderSide(color: Color(0xFF0058FF)),
  //           ),
  //           contentPadding:
  //               const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
  //         ),
  //         style: FontManager.getCustomStyle(
  //           fontSize: 14,
  //           fontWeight: FontWeight.w400,
  //           fontFamily: FontManager.fontFamilyTiemposText,
  //           color: Colors.black,
  //         ),
  //       ),
  //     ],
  //   );
  // }

  static void _showValidationRulesDialog(
    BuildContext context, {
    String? attribute,
    String? validationFunction,
    String? successCondition,
    String? failureCondition,
    String? successMessage,
    String? failureMessage,
    Function(String, String, String, String, String, String)? onApply,
  }) {
    final attributeController = TextEditingController(text: attribute ?? '');
    final validationFunctionController =
        TextEditingController(text: validationFunction ?? '');
    final successConditionController =
        TextEditingController(text: successCondition ?? '');
    final failureConditionController =
        TextEditingController(text: failureCondition ?? '');
    final successMessageController =
        TextEditingController(text: successMessage ?? '');
    final failureMessageController =
        TextEditingController(text: failureMessage ?? '');

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            // Function to check if all required fields are valid
            bool areAllFieldsValid() {
              return attributeController.text.trim().isNotEmpty &&
                  validationFunctionController.text.trim().isNotEmpty &&
                  successConditionController.text.trim().isNotEmpty &&
                  failureConditionController.text.trim().isNotEmpty &&
                  successMessageController.text.trim().isNotEmpty &&
                  failureMessageController.text.trim().isNotEmpty;
            }

            return Dialog(
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12)),
              child: Container(
                width: 600,
                padding: const EdgeInsets.all(24),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Row(
                      children: [
                        Text(
                          'Validation Rules',
                          style: FontManager.getCustomStyle(
                            fontSize: ResponsiveFontSizes.bodyLarge(context),
                            fontWeight: FontWeight.w600,
                            fontFamily: FontManager.fontFamilyTiemposText,
                            color: Colors.black,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: () => Navigator.of(context).pop(),
                          icon: const Icon(Icons.close),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Form fields in 2x3 grid
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Left column
                        Expanded(
                          child: Column(
                            children: [
                              // Attribute field
                              _buildValidationDialogField(
                                context,
                                'Attribute',
                                attributeController,
                                'Field to validate (e.g., Customer.email,Order.quantity)',
                                onChanged: () => setState(() {}),
                              ),
                              const SizedBox(height: 16),

                              // Success Condition field
                              _buildValidationDialogField(
                                context,
                                'Success Condition',
                                successConditionController,
                                'Condition for valid input (e.g., age >= 18, phone LENGTH == 10)',
                                onChanged: () => setState(() {}),
                              ),
                              const SizedBox(height: 16),

                              // Success Message field
                              _buildValidationDialogField(
                                context,
                                'Success Message',
                                successMessageController,
                                'Message shown when validation passes (e.g., Valid age, Phone number correct)',
                                onChanged: () => setState(() {}),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(width: 16),

                        // Right column
                        Expanded(
                          child: Column(
                            children: [
                              // Validation Function field
                              _buildValidationDialogField(
                                context,
                                'Validation Function',
                                validationFunctionController,
                                'Validation rule to apply (e.g., validate_email_format, range_check)',
                                onChanged: () => setState(() {}),
                              ),
                              const SizedBox(height: 16),

                              // Failure Condition field
                              _buildValidationDialogField(
                                context,
                                'Failure Condition',
                                failureConditionController,
                                'Condition for invalid input (e.g., endDate<startDate, name IS_NULL)',
                                onChanged: () => setState(() {}),
                              ),
                              const SizedBox(height: 16),

                              // Failure Message field
                              _buildValidationDialogField(
                                context,
                                'Failure Message',
                                failureMessageController,
                                'Error message shown when validation fails (e.g., Invalid email format, Name is required)',
                                onChanged: () => setState(() {}),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 32),

                    // Buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: Text(
                            'Cancel',
                            style: FontManager.getCustomStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.grey[600],
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        ElevatedButton(
                          onPressed: areAllFieldsValid()
                              ? () {
                                  onApply?.call(
                                    attributeController.text,
                                    validationFunctionController.text,
                                    successConditionController.text,
                                    failureConditionController.text,
                                    successMessageController.text,
                                    failureMessageController.text,
                                  );
                                  Navigator.of(context).pop();
                                }
                              : null, // Disable button when fields are empty
                          style: ElevatedButton.styleFrom(
                            backgroundColor: areAllFieldsValid()
                                ? const Color(0xFF0058FF)
                                : Colors.grey[300], // Grey when disabled
                            foregroundColor: areAllFieldsValid()
                                ? Colors.white
                                : Colors.grey[600], // Grey text when disabled
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                          child: Text(
                            'Apply This',
                            style: FontManager.getCustomStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: areAllFieldsValid()
                                  ? Colors.white
                                  : Colors.grey[600],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

// Updated _buildValidationDialogField method with onChanged callback
  static Widget _buildValidationDialogField(
    BuildContext context,
    String label,
    TextEditingController controller,
    String tooltipText, {
    VoidCallback? onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w500,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
            Tooltip(
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 8),
              message: tooltipText,
              decoration: BoxDecoration(
                color: const Color(0xFFeff6ff),
                borderRadius: BorderRadius.circular(6),
              ),
              textStyle: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodySmall(context),
                fontWeight: FontWeight.w500,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
              // preferBelow: false,
              child: Container(
                width: 16,
                height: 16,
                decoration: const BoxDecoration(
                  // color: Color(0xFF0058FF),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.info_outline_rounded,
                  size: 18,
                  color: Color(0xFF0058FF),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        TextField(
          controller: controller,
          onChanged: (value) => onChanged?.call(), // Added onChanged callback
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: Color(0xFFE5E7EB)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: Color(0xFFE5E7EB)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
              borderSide: const BorderSide(color: Color(0xFF0058FF)),
            ),
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
          style: FontManager.getCustomStyle(
            fontSize: 14,
            fontWeight: FontWeight.w400,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: Colors.black,
          ),
        ),
      ],
    );
  }

  /// Show validation test form
  static void _showValidationTestForm(
      BuildContext context, List<Map<String, dynamic>> validationRules) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) =>
            ValidationTestForm(validationRules: validationRules),
      ),
    );
  }
}

/// Validation Test Form - Shows how validation rules work in practice
class ValidationTestForm extends StatefulWidget {
  final List<Map<String, dynamic>> validationRules;

  const ValidationTestForm({Key? key, required this.validationRules})
      : super(key: key);

  @override
  State<ValidationTestForm> createState() => _ValidationTestFormState();
}

class _ValidationTestFormState extends State<ValidationTestForm> {
  final Map<String, String> _formData = {};
  final Map<String, ValidationResult> _validationResults = {};
  final Map<String, TextEditingController> _controllers = {};

  @override
  void initState() {
    super.initState();
    // Create controllers for each unique attribute
    final uniqueAttributes = widget.validationRules
        .map((rule) => rule['attribute'] as String)
        .toSet()
        .toList();

    for (final attribute in uniqueAttributes) {
      _controllers[attribute] = TextEditingController();
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _onFieldValidationChanged(
      String fieldName, String value, bool isValid, String message) {
    setState(() {
      _formData[fieldName] = value;
      _validationResults[fieldName] = ValidationResult(
        isValid: isValid,
        message: message,
        rule: widget.validationRules.firstWhere(
          (rule) => rule['attribute'] == fieldName,
          orElse: () => {},
        ),
      );
    });
  }

  bool get _isFormValid {
    return ValidationEngine.isFormValid(_validationResults) &&
        _validationResults.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    final uniqueAttributes = widget.validationRules
        .map((rule) => rule['attribute'] as String)
        .toSet()
        .toList();

    return Scaffold(
      appBar: AppBar(
        title: const Text('Validation Test Form'),
        backgroundColor: const Color(0xFF0058FF),
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Text(
              'Test Your Validation Rules',
              style: FontManager.getCustomStyle(
                fontSize: 24,
                fontWeight: FontWeight.w700,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.black,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'This form uses the validation rules you created. Try entering different values to see how validation works.',
              style: FontManager.getCustomStyle(
                fontSize: ResponsiveFontSizes.bodyMedium(context),
                fontWeight: FontWeight.w400,
                fontFamily: FontManager.fontFamilyTiemposText,
                color: Colors.grey[600],
              ),
            ),

            const SizedBox(height: 32),

            // Dynamic form fields based on validation rules
            ...uniqueAttributes.map((attribute) {
              final rule = widget.validationRules.firstWhere(
                (r) => r['attribute'] == attribute,
              );

              return Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: ValidatedTextField(
                  fieldName: attribute,
                  label: _getFieldLabel(attribute),
                  controller: _controllers[attribute]!,
                  validationRules: widget.validationRules,
                  keyboardType: _getKeyboardType(rule['validationFunction']),
                  hintText: _getHintText(attribute, rule),
                  prefixIcon: _getFieldIcon(attribute),
                  onValidationChanged: (value, isValid, message) =>
                      _onFieldValidationChanged(
                          attribute, value, isValid, message),
                ),
              );
            }).toList(),

            const SizedBox(height: 32),

            // Validation Summary
            if (_validationResults.isNotEmpty) ...[
              ValidationSummary(
                validationResults: _validationResults,
                showOnlyErrors: true,
              ),
              const SizedBox(height: 20),
            ],

            // Form Status
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: _isFormValid ? Colors.green[50] : Colors.orange[50],
                border: Border.all(
                  color:
                      _isFormValid ? Colors.green[200]! : Colors.orange[200]!,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    _isFormValid ? Icons.check_circle : Icons.warning,
                    color:
                        _isFormValid ? Colors.green[600] : Colors.orange[600],
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _isFormValid
                          ? 'All validation rules passed!'
                          : 'Some validation rules failed',
                      style: FontManager.getCustomStyle(
                        fontSize: ResponsiveFontSizes.bodyMedium(context),
                        fontWeight: FontWeight.w500,
                        fontFamily: FontManager.fontFamilyTiemposText,
                        color: _isFormValid
                            ? Colors.green[800]
                            : Colors.orange[800],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getFieldLabel(String attribute) {
    return attribute.split('.').last.replaceAll('_', ' ').toUpperCase();
  }

  TextInputType? _getKeyboardType(String? validationFunction) {
    switch (validationFunction?.toLowerCase()) {
      case 'validate_email_format':
        return TextInputType.emailAddress;
      case 'validate_phone_format':
        return TextInputType.phone;
      case 'validate_number':
        return TextInputType.number;
      case 'validate_url':
        return TextInputType.url;
      default:
        return TextInputType.text;
    }
  }

  String _getHintText(String attribute, Map<String, dynamic> rule) {
    final function = rule['validationFunction'] as String? ?? '';
    switch (function.toLowerCase()) {
      case 'validate_email_format':
        return 'Enter a valid email address';
      case 'validate_phone_format':
        return 'Enter a valid phone number';
      case 'validate_number':
        return 'Enter a number';
      case 'validate_url':
        return 'Enter a valid URL';
      case 'required_field':
        return 'This field is required';
      default:
        return 'Enter ${attribute.split('.').last}';
    }
  }

  Widget? _getFieldIcon(String attribute) {
    if (attribute.toLowerCase().contains('email')) {
      return const Icon(Icons.email_outlined);
    } else if (attribute.toLowerCase().contains('phone')) {
      return const Icon(Icons.phone_outlined);
    } else if (attribute.toLowerCase().contains('name')) {
      return const Icon(Icons.person_outlined);
    } else if (attribute.toLowerCase().contains('age')) {
      return const Icon(Icons.calendar_today_outlined);
    } else if (attribute.toLowerCase().contains('website')) {
      return const Icon(Icons.language_outlined);
    }
    return const Icon(Icons.text_fields_outlined);
  }
}

/// Hover Button Widget
class _HoverButton extends StatefulWidget {
  final String text;
  final String hoverText;
  final VoidCallback onPressed;
  final bool isPrimary;

  const _HoverButton({
    required this.text,
    required this.hoverText,
    required this.onPressed,
    this.isPrimary = false,
  });

  @override
  State<_HoverButton> createState() => _HoverButtonState();
}

class _HoverButtonState extends State<_HoverButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: ElevatedButton(
        onPressed: widget.onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: widget.isPrimary
              ? const Color(0xFF0058FF)
              : const Color(0xFF0058FF),
          foregroundColor: widget.isPrimary ? Colors.white : Colors.black,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(6),
          ),
        ),
        child: Text(
          _isHovered ? widget.hoverText : widget.text,
          style: FontManager.getCustomStyle(
            fontSize: ResponsiveFontSizes.bodyMedium(context),
            fontWeight: FontWeight.w500,
            fontFamily: FontManager.fontFamilyTiemposText,
            color: widget.isPrimary ? Colors.white : Colors.white,
          ),
        ),
      ),
    );
  }
}

/// Accordion Solution Sections Widget - Only one section can be expanded at a time
class _AccordionSolutionSections extends StatefulWidget {
  final SolutionCreationModel solution;

  const _AccordionSolutionSections({required this.solution});

  @override
  State<_AccordionSolutionSections> createState() =>
      _AccordionSolutionSectionsState();
}

class _AccordionSolutionSectionsState
    extends State<_AccordionSolutionSections> {
  int? _expandedIndex; // Track which section is currently expanded

  // Mutable lists initialized from model data
  late List<Map<String, dynamic>> _outputStackRows;
  late List<Map<String, dynamic>> _validationStackRows;
  late List<Map<String, dynamic>> _uiStackRows;
  late List<Map<String, dynamic>> _mappingStackRows;
  late List<Map<String, dynamic>> _nestedFunctionPathwaysRows;
  late List<Map<String, dynamic>> _executionPathwayRows;

  @override
  void initState() {
    super.initState();
    _initializeStackRows();
  }

  void _initializeStackRows() {
    // Initialize output stack rows from model or use default data
    if (widget.solution.outputStack?.rows != null) {
      _outputStackRows = widget.solution.outputStack!.rows!
          .map((row) => {
                'entity': row.entity,
                'attribute': row.attribute,
                'required': row.required,
              })
          .toList();
    } else {
      _outputStackRows = [
        {'entity': 'Customer', 'attribute': 'Customer ID', 'required': true},
      ];
    }

    // Initialize validation stack rows from model or use default data
    if (widget.solution.validationStack?.rows != null) {
      _validationStackRows = widget.solution.validationStack!.rows!
          .map((row) => {
                'attribute': row.attribute,
                'validationFunction': row.validationFunction,
                'successCondition': row.successCondition,
                'failureCondition': row.failureCondition,
                'successMessage': row.successMessage,
                'failureMessage': row.failureMessage,
              })
          .toList();
    } else {
      _validationStackRows = [
        {
          'attribute': 'Customer.email',
          'validationFunction': 'validate_email_format',
          'successCondition': 'true',
          'failureCondition': 'false',
          'successMessage': 'Valid email format',
          'failureMessage': 'Please enter a valid email address',
        },
        {
          'attribute': 'Customer.name',
          'validationFunction': 'required_field',
          'successCondition': 'true',
          'failureCondition': 'false',
          'successMessage': 'Valid name',
          'failureMessage': 'Name is required',
        },
        {
          'attribute': 'Customer.phone',
          'validationFunction': 'validate_phone_format',
          'successCondition': 'false',
          'failureCondition': 'true',
          'successMessage': 'Valid phone number',
          'failureMessage': 'Please enter a valid phone number',
        },
      ];
    }

    // Initialize UI stack rows from model or use default data
    if (widget.solution.uiStack?.rows != null) {
      _uiStackRows = widget.solution.uiStack!.rows!
          .map((row) => {
                'entityName': row.entityName,
                'displayName': row.displayName,
                'widgetType': row.widgetType,
                'selectionType': row.selectionType,
                'editingRights': row.editingRights,
                'pagination': row.pagination,
                'search': row.search,
                'filters': row.filters,
                'sort': row.sort,
                'dataFiltering': row.dataFiltering,
                'dataSorting': row.dataSorting,
                'conditionerColors': row.conditionerColors,
              })
          .toList();
    } else {
      _uiStackRows = [
        {
          'entityName': 'Customer Details',
          'displayName': 'Customer Details',
          'widgetType': 'Table',
          'selectionType': 'Single',
          'editingRights': 'Admin',
          'pagination': 5,
          'search': 'Driver Name',
          'filters': 'Vehicle Type',
          'sort': 'Shipping Date',
          'dataFiltering': [
            {
              'attribute': 'Shipping Date',
              'value': 'Current Date',
              'operator': '<'
            }
          ],
          'dataSorting': [
            {'attribute': 'Shipping Date', 'order': 'Ascend'}
          ],
          'conditionerColors': [
            {
              'attribute': 'Status',
              'value': 'Delivered',
              'operator': '=',
              'fillColor': '#00FF00',
              'fontColor': '#000000'
            }
          ],
        },
      ];
    }

    // Initialize mapping stack rows from model or use default data
    if (widget.solution.mappingStack?.rows != null) {
      _mappingStackRows = widget.solution.mappingStack!.rows!
          .map((row) => {
                'sourceGOName': row.sourceGOName,
                'sourceLOName': row.sourceLOName,
                'sourceType': row.sourceType,
                'sourceEntity': row.sourceEntity,
                'sourceAttribute': row.sourceAttribute,
                'targetGOName': row.targetGOName,
                'targetLOName': row.targetLOName,
                'targetType': row.targetType,
                'targetEntity': row.targetEntity,
                'targetAttribute': row.targetAttribute,
              })
          .toList();
    } else {
      _mappingStackRows = [
        {
          'sourceGOName': 'CustomerMgmt',
          'sourceLOName': 'CustomerOnboarding',
          'sourceType': 'OUTPUT',
          'sourceEntity': 'Customer',
          'sourceAttribute': 'customer_id',
          'targetGOName': 'CustomerManagement',
          'targetLOName': 'SendWelcomeEmail',
          'targetType': 'INPUT',
          'targetEntity': 'Customer',
          'targetAttribute': 'customer_id',
        },
      ];
    }

    // Initialize nested function pathways rows from model or use default data
    if (widget.solution.nestedFunctionPathways?.rows != null) {
      _nestedFunctionPathwaysRows =
          widget.solution.nestedFunctionPathways!.rows!
              .map((row) => {
                    'functionId': row.functionId,
                    'functionName': row.functionName,
                    'functionType': row.functionType,
                    'description': row.description,
                  })
              .toList();
    } else {
      _nestedFunctionPathwaysRows = [
        {
          'functionId': 'NF1',
          'functionName': 'validate_customer_data',
          'functionType': 'conditional_assignment',
          'description': 'Validates customer data before processing',
        },
        {
          'functionId': 'NF2',
          'functionName': 'send_verification_email',
          'functionType': 'send_email',
          'description': 'Sends verification email to customer',
        },
        {
          'functionId': 'NF3',
          'functionName': 'create_customer_record',
          'functionType': 'create_record',
          'description': 'Creates new customer record in database',
        },
      ];
    }

    // Initialize execution pathway rows from model or use default data
    if (widget.solution.executionPathway?.rows != null) {
      _executionPathwayRows = widget.solution.executionPathway!.rows!
          .map((row) => {
                'condition': row.condition,
                'functionName': row.functionName,
                'description': row.description,
              })
          .toList();
    } else {
      _executionPathwayRows = [
        {
          'condition': 'Customer.verification_status = "verified"',
          'functionName': 'SendWelcomeEmail',
          'description': 'Route to welcome email after successful verification',
        },
      ];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // LO Details - Index 0
        _buildAccordionSection(
          context,
          0,
          'LO Details',
          widget.solution.loDetails?.status ?? 'Partial Completion',
          '${widget.solution.loDetails?.count ?? 1} LO',
          SolutionExpansionHandler._buildLODetailsContent(
              context, widget.solution),
        ),

        // Inputs Stack - Index 1
        _buildAccordionSection(
          context,
          1,
          'Inputs Stack',
          widget.solution.inputsStack?.status ?? 'Partial Completion',
          '${widget.solution.inputsStack?.attributesCount ?? 25} Attributes',
          SolutionExpansionHandler._buildInputsStackContent(
              context, widget.solution),
        ),

        // Output Stack - Index 2
        _buildAccordionSection(
          context,
          2,
          'Output Stack',
          widget.solution.outputStack?.status ?? 'Partial Completion',
          'Configured',
          SolutionExpansionHandler._buildOutputStackContentWithData(
            context,
            widget.solution,
            _outputStackRows,
            onAddRow: (entity, attribute, required) {
              setState(() {
                _outputStackRows.add({
                  'entity': entity,
                  'attribute': attribute,
                  'required': required,
                });
              });
            },
            onEditRow: (index, entity, attribute, required) {
              setState(() {
                _outputStackRows[index] = {
                  'entity': entity,
                  'attribute': attribute,
                  'required': required,
                };
              });
            },
            onDeleteRow: (index) {
              setState(() {
                _outputStackRows.removeAt(index);
              });
            },
          ),
        ),

        // Validation Stack - Index 3
        _buildAccordionSection(
          context,
          3,
          'Validation Stack',
          widget.solution.validationStack?.status ?? 'Missing',
          '${widget.solution.validationStack?.rulesCount ?? 2} Rules',
          SolutionExpansionHandler._buildValidationStackContentWithData(
            context,
            widget.solution,
            _validationStackRows,
            onAddRow: (attribute, validationFunction, successCondition,
                failureCondition, successMessage, failureMessage) {
              setState(() {
                _validationStackRows.add({
                  'attribute': attribute,
                  'validationFunction': validationFunction,
                  'successCondition': successCondition,
                  'failureCondition': failureCondition,
                  'successMessage': successMessage,
                  'failureMessage': failureMessage,
                });
              });
            },
            onEditRow: (index, attribute, validationFunction, successCondition,
                failureCondition, successMessage, failureMessage) {
              setState(() {
                _validationStackRows[index] = {
                  'attribute': attribute,
                  'validationFunction': validationFunction,
                  'successCondition': successCondition,
                  'failureCondition': failureCondition,
                  'successMessage': successMessage,
                  'failureMessage': failureMessage,
                };
              });
            },
            onDeleteRow: (index) {
              setState(() {
                _validationStackRows.removeAt(index);
              });
            },
          ),
        ),

        // UI Stack - Index 4
        _buildAccordionSection(
          context,
          4,
          'UI Stack',
          widget.solution.uiStack?.status ?? 'Missing',
          '${widget.solution.uiStack?.configuredCount ?? 2} Configured',
          SolutionExpansionHandler._buildUIStackContentWithData(
            context,
            widget.solution,
            _uiStackRows,
            onAddRow: (entityName,
                displayName,
                widgetType,
                selectionType,
                editingRights,
                pagination,
                search,
                filters,
                sort,
                dataFiltering,
                dataSorting,
                conditionerColors) {
              setState(() {
                _uiStackRows.add({
                  'entityName': entityName,
                  'displayName': displayName,
                  'widgetType': widgetType,
                  'selectionType': selectionType,
                  'editingRights': editingRights,
                  'pagination': pagination,
                  'search': search,
                  'filters': filters,
                  'sort': sort,
                  'dataFiltering': dataFiltering,
                  'dataSorting': dataSorting,
                  'conditionerColors': conditionerColors,
                });
              });
            },
            onEditRow: (index,
                entityName,
                displayName,
                widgetType,
                selectionType,
                editingRights,
                pagination,
                search,
                filters,
                sort,
                dataFiltering,
                dataSorting,
                conditionerColors) {
              setState(() {
                _uiStackRows[index] = {
                  'entityName': entityName,
                  'displayName': displayName,
                  'widgetType': widgetType,
                  'selectionType': selectionType,
                  'editingRights': editingRights,
                  'pagination': pagination,
                  'search': search,
                  'filters': filters,
                  'sort': sort,
                  'dataFiltering': dataFiltering,
                  'dataSorting': dataSorting,
                  'conditionerColors': conditionerColors,
                };
              });
            },
            onDeleteRow: (index) {
              setState(() {
                _uiStackRows.removeAt(index);
              });
            },
          ),
        ),

        // Mapping Stack - Index 5
        _buildAccordionSection(
          context,
          5,
          'Mapping Stack',
          '',
          '${widget.solution.mappingStack?.mappingCount ?? 3} Mapping',
          SolutionExpansionHandler._buildMappingStackContentWithData(
            context,
            widget.solution,
            _mappingStackRows,
            onAddRow: (sourceGOName,
                sourceLOName,
                sourceType,
                sourceEntity,
                sourceAttribute,
                targetGOName,
                targetLOName,
                targetType,
                targetEntity,
                targetAttribute) {
              setState(() {
                _mappingStackRows.add({
                  'sourceGOName': sourceGOName,
                  'sourceLOName': sourceLOName,
                  'sourceType': sourceType,
                  'sourceEntity': sourceEntity,
                  'sourceAttribute': sourceAttribute,
                  'targetGOName': targetGOName,
                  'targetLOName': targetLOName,
                  'targetType': targetType,
                  'targetEntity': targetEntity,
                  'targetAttribute': targetAttribute,
                });
              });
            },
            onEditRow: (index,
                sourceGOName,
                sourceLOName,
                sourceType,
                sourceEntity,
                sourceAttribute,
                targetGOName,
                targetLOName,
                targetType,
                targetEntity,
                targetAttribute) {
              setState(() {
                _mappingStackRows[index] = {
                  'sourceGOName': sourceGOName,
                  'sourceLOName': sourceLOName,
                  'sourceType': sourceType,
                  'sourceEntity': sourceEntity,
                  'sourceAttribute': sourceAttribute,
                  'targetGOName': targetGOName,
                  'targetLOName': targetLOName,
                  'targetType': targetType,
                  'targetEntity': targetEntity,
                  'targetAttribute': targetAttribute,
                };
              });
            },
            onDeleteRow: (index) {
              setState(() {
                _mappingStackRows.removeAt(index);
              });
            },
          ),
        ),

        // Nested Function Pathways - Index 6
        _buildAccordionSection(
          context,
          6,
          'Nested Function Pathways',
          '',
          '${_nestedFunctionPathwaysRows.length} Pathways',
          SolutionExpansionHandler._buildNestedFunctionPathwaysContentWithData(
            context,
            widget.solution,
            _nestedFunctionPathwaysRows,
            onAddRow: (functionId, functionName, functionType, description) {
              setState(() {
                _nestedFunctionPathwaysRows.add({
                  'functionId': functionId,
                  'functionName': functionName,
                  'functionType': functionType,
                  'description': description,
                });
              });
            },
            onEditRow:
                (index, functionId, functionName, functionType, description) {
              setState(() {
                _nestedFunctionPathwaysRows[index] = {
                  'functionId': functionId,
                  'functionName': functionName,
                  'functionType': functionType,
                  'description': description,
                };
              });
            },
            onDeleteRow: (index) {
              setState(() {
                _nestedFunctionPathwaysRows.removeAt(index);
              });
            },
          ),
        ),

        // Execution Pathway - Index 7
        _buildAccordionSection(
          context,
          7,
          'Execution Pathway',
          'Not Configured',
          '${_executionPathwayRows.length} Routes',
          SolutionExpansionHandler._buildExecutionPathwayContentWithData(
            context,
            widget.solution,
            _executionPathwayRows,
            onAddRow: (condition, functionName, description) {
              setState(() {
                _executionPathwayRows.add({
                  'condition': condition,
                  'functionName': functionName,
                  'description': description,
                });
              });
            },
            onEditRow: (index, condition, functionName, description) {
              setState(() {
                _executionPathwayRows[index] = {
                  'condition': condition,
                  'functionName': functionName,
                  'description': description,
                };
              });
            },
            onDeleteRow: (index) {
              setState(() {
                _executionPathwayRows.removeAt(index);
              });
            },
          ),
        ),
      ],
    );
  }

  Widget _buildAccordionSection(
    BuildContext context,
    int index,
    String title,
    String status,
    String count,
    Widget content,
  ) {
    final bool isExpanded = _expandedIndex == index;
    final bool isInputsStack = title == 'Inputs Stack';

    Color statusColor = Colors.grey;
    Color statusBgColor = Colors.grey[100]!;

    // Set colors based on status
    if (status == 'Partial Completion') {
      statusColor = const Color(0xFF92400E);
      statusBgColor = const Color(0xFFFEF3C7);
    } else if (status == 'Missing') {
      statusColor = const Color(0xFF991B1B);
      statusBgColor = const Color(0xFFFEE2E2);
    }

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE5E7EB), width: 1),
        borderRadius: BorderRadius.circular(4),
        color: Colors.white,
      ),
      child: Column(
        children: [
          // Header
          InkWell(
            onTap: () {
              setState(() {
                // If clicking on already expanded section, collapse it
                // If clicking on different section, expand it and collapse others
                _expandedIndex = isExpanded ? null : index;
              });
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Row(
                children: [
                  // Left side content (title, status, library button)
                  Expanded(
                    child: Row(
                      children: [
                        // Title (takes only the space it needs)
                        Flexible(
                          child: Text(
                            title,
                            style: FontManager.getCustomStyle(
                              fontSize: _getResponsiveValueFontSize(context),
                              fontWeight: isExpanded
                                  ? FontWeight.w600
                                  : FontWeight.w300,
                              fontFamily: FontManager.fontFamilyTiemposText,
                              color: Colors.black,
                            ),
                            overflow: TextOverflow.visible,
                            maxLines: 2,
                          ),
                        ),

                        const SizedBox(width: AppSpacing.xxxl),

                        // Status
                        if (status.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 8, vertical: 2),
                            decoration: BoxDecoration(
                              color: statusBgColor,
                              // borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              status,
                              style: FontManager.getCustomStyle(
                                  fontSize:
                                      _getResponsiveValueFontSize(context),
                                  fontWeight: isExpanded
                                      ? FontWeight.w600
                                      : FontWeight.w300,
                                  fontFamily: FontManager.fontFamilyTiemposText,
                                  color: statusColor),
                              overflow: TextOverflow.visible,
                              maxLines: 2,
                            ),
                          ),

                        // + Library button for Inputs Stack (only when expanded)
                        if (isInputsStack && isExpanded) ...[
                          const SizedBox(width: 8),
                          ElevatedButton.icon(
                            onPressed: () {
                              _showLibraryDialog(context);
                            },
                            icon: const Icon(Icons.add, size: 16),
                            label: Text(
                              'Library',
                              style: FontManager.getCustomStyle(
                                fontSize:
                                    ResponsiveFontSizes.bodySmall(context),
                                fontWeight: FontWeight.w500,
                                fontFamily: FontManager.fontFamilyTiemposText,
                                color: Colors.white,
                              ),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF0058FF),
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              minimumSize: Size.zero,
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Right side content (count and arrow) - always at the end
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        count,
                        style: FontManager.getCustomStyle(
                          fontSize: _getResponsiveValueFontSize(context),
                          fontWeight:
                              isExpanded ? FontWeight.w600 : FontWeight.w300,
                          fontFamily: FontManager.fontFamilyTiemposText,
                          color: Colors.black,
                        ),
                        overflow: TextOverflow.visible,
                        maxLines: 2,
                      ),
                      const SizedBox(width: 8),
                      AnimatedRotation(
                        turns: isExpanded ? 0.5 : 0.0,
                        duration: const Duration(milliseconds: 200),
                        child: Icon(
                          Icons.keyboard_arrow_down,
                          color: Colors.grey[600],
                          size: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (isExpanded)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: const BoxDecoration(
                border: Border(
                  top: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: content,
            ),
        ],
      ),
    );
  }

  /// Show Library Dialog
  void _showLibraryDialog(BuildContext context) {
    SolutionExpansionHandler._showAttributesDialog(
      context,
      onApply: (String entity, String attribute, bool required) {
        // Add new row to output stack data
        setState(() {
          _outputStackRows.add({
            'entity': entity,
            'attribute': attribute,
            'required': required,
          });
          // Auto-expand Output Stack
          _expandedIndex = 2; // Output Stack is index 2
        });
      },
    );
  }

  double _getResponsiveValueFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    if (screenWidth > 1920) {
      return 18.0; // Extra Large
    } else if (screenWidth >= 1440) {
      return 16.0; // Large
    } else if (screenWidth >= 1280) {
      return 12.0; // Medium
    } else {
      return 12.0; // Default for very small screens
    }
  }
}

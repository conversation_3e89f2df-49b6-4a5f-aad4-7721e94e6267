import 'package:dio/dio.dart';
import 'package:nsl/models/role_model.dart';
import 'package:nsl/utils/logger.dart';

class RolesService {
  static const String _baseUrl = 'http://10.26.1.52:8102';
  static const String _rolesEndpoint = '/api/roles/fetch/roles';

  final Dio _dio;

  RolesService() : _dio = Dio() {
    _dio.options.baseUrl = _baseUrl;
    _dio.options.connectTimeout = const Duration(seconds: 30);
    _dio.options.receiveTimeout = const Duration(seconds: 30);
    _dio.options.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    // Add interceptor for logging
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) {
          Logger.info('Roles API Request: ${options.method} ${options.uri}');
          handler.next(options);
        },
        onResponse: (response, handler) {
          Logger.info('Roles API Response: ${response.statusCode}');
          handler.next(response);
        },
        onError: (error, handler) {
          Logger.error('Roles API Error: ${error.message}');
          handler.next(error);
        },
      ),
    );
  }

  /// Fetches the list of roles from the API
  Future<RoleModel> fetchRoles() async {
    try {
      Logger.info('Fetching roles from API...');

      final response = await _dio.get(_rolesEndpoint);

      if (response.statusCode == 200) {
        final roleModel = RoleModel.fromJson(response.data);
        Logger.info('Successfully fetched ${roleModel.postgresRoles?.length ?? 0} postgres roles');
        return roleModel;
      } else {
        Logger.error('Failed to fetch roles: ${response.statusCode}');
        throw Exception('Failed to fetch roles: ${response.statusCode}');
      }
    } on DioException catch (e) {
      Logger.error('Dio error while fetching roles: ${e.message}');
      String errorMessage = 'Network error occurred';

      if (e.type == DioExceptionType.connectionTimeout) {
        errorMessage = 'Connection timeout. Please check your internet connection.';
      } else if (e.type == DioExceptionType.receiveTimeout) {
        errorMessage = 'Request timeout. Please try again.';
      } else if (e.response != null) {
        errorMessage = 'Server error: ${e.response?.statusCode}';
      }

      throw Exception(errorMessage);
    } catch (e) {
      Logger.error('Unexpected error while fetching roles: $e');
      throw Exception('An unexpected error occurred: $e');
    }
  }

  /// Dispose method to clean up resources
  void dispose() {
    _dio.close();
  }
}
